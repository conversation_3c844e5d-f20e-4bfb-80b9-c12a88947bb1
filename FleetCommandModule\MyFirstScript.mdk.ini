; This file is project specific and should be checked in to source control.

[mdk]
; This is a programmable block script project.
; You should not change this.
type=programmableblock

; Toggle trace (on|off) (verbose output)
trace=off

; What type of minification to use (none|trim|stripcomments|lite|full)
; none: No minification
; trim: Removes unused types (NOT members).
; stripcomments: trim + removes comments.
; lite: stripcomments + removes leading/trailing whitespace.
; full: lite + renames identifiers to shorter names.
minify=none

; A list of files and folder to ignore when creating the script.
; This is a comma separated list of glob patterns. 
; See https://code.visualstudio.com/docs/editor/glob-patterns
ignores=obj/**/*,MDK/**/*,**/*.debug.cs
