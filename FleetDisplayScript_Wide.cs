/*
 * Rdav's Fleet Command Beta Ver_008_Updated_Wide
 * Wide LCD Version - Optimized for Wide LCD Panels (178x100 resolution)
 * 
 * CHANGES FROM ORIGINAL:
 * - Modified for wide LCD panels (178x100 instead of 100x100)
 * - Adjusted UI layout for wider aspect ratio
 * - Extended tactical map display area
 * - Repositioned UI elements for better wide screen utilization
 * - Updated to use IGC (Inter-Grid Communication) system
 * - Removed deprecated antenna communication methods
 * - Updated for current Space Engineers API compatibility
 * 
 * REQUIRED BLOCKS:
 * - LCD Panel named "RFC_PANEL_WIDE" (Wide LCD Panel)
 * - Remote Control named "RFC_RC" 
 * - Programmable Block named "CENTRAL_COMMAND"
 * - Gyroscopes for mouse control
 * 
 * SETUP:
 * 1. Place a Wide LCD Panel and name it "RFC_PANEL_WIDE"
 * 2. Ensure you have the required control blocks
 * 3. Run this script on a programmable block
 * 4. Use ship rotation controls as mouse, roll controls as mouse buttons
 */

using Sandbox.Game.EntityComponents;
using Sandbox.ModAPI.Ingame;
using Sandbox.ModAPI.Interfaces;
using SpaceEngineers.Game.ModAPI.Ingame;
using System.Collections.Generic;
using System.Collections;
using System.Linq;
using System.Text;
using System;
using VRage.Collections;
using VRage.Game.Components;
using VRage.Game.GUI.TextPanel;
using VRage.Game.ModAPI.Ingame.Utilities;
using VRage.Game.ModAPI.Ingame;
using VRage.Game.ObjectBuilders.Definitions;
using VRage.Game;
using VRage.Utils;
using VRageMath;

namespace IngameScript
{
    partial class Program : MyGridProgram
    {
        #region Version and Setup
        /*
         * Rdav's Fleet Command Beta Ver_008_Updated_Wide
         * Wide LCD Display Version
         * 
         * Updated Features:
         * - Wide LCD support (178x100 resolution)
         * - Extended tactical display area
         * - Improved UI layout for wide screens
         * - Modern IGC communication system
         * - Current Space Engineers API compatibility
         */
        #endregion

        #region Global Variables
        string VERSION = "Rdav's Fleet Command Beta Ver_008_Updated_Wide";
        
        // IGC Communication
        const string IGCTagIN = "RFC_FLEET_IN";
        const string IGCTagOUT = "RFC_FLEET_OUT";
        IMyBroadcastListener IGCListener;
        
        // Display Colors
        const char P = medBlue; //Primary System Colour (your own ships)
        const char B = ' '; //Background Colour
        const char L1 = black; //Layer1background1colour
        const char L2 = mediumGray; //Layer2background1colour
        
        // Wide LCD Display Arrays (178x100 instead of 100x100)
        public char[,] BDRAW = new char[ROWS_CT, COLUMNS_CT]; //Stores Background
        char[,] DRAW = new char[ROWS_CT, COLUMNS_CT]; //Temporary Assigner

        #region Color Definitions
        const char red = '\uE200';
        const char medRed = '\uE1C0';
        const char darkRed = '\uE180';
        const char orange = '\uE140';
        const char yellow = '\uE100';
        const char lightYellow = '\uE0C0';
        const char green = '\uE080';
        const char lightGreen = '\uE040';
        const char cyan = '\uE020';
        const char lightBlue = '\uE010';
        const char blue = '\uE008';
        const char medBlue = '\uE006';
        const char darkBlue = '\uE004';
        const char purple = '\uE002';
        const char magenta = '\uE001';
        const char white = '\uE0FF';
        const char lightGray = '\uE0BF';
        const char mediumGray = '\uE07F';
        const char darkGray = '\uE03F';
        const char black = '\uE000';
        #endregion

        // Wide LCD Screen Dimensions and Limits
        const int ROWS_CT = 100;      // Height remains 100
        const int COLUMNS_CT = 178;   // Width extended to 178 for wide LCD
        char[] ALLOC = new char[17901]; // Screen Size (178*100 + 101 for newlines)
        
        // UI Layout adjusted for wide screen
        int ROW_UI_START = 7;
        int ROW_UI_END = 94;
        int ROW_ZOOM_ICON = 4;
        int COL_ZOOM_ICON1 = 156;     // Moved zoom icons to the right
        int COL_ZOOM_ICON2 = 170;     // Positioned for wide screen
        
        // Mouse and UI Variables
        double POS_Y = 89; // Mouse X pos (adjusted for wide screen)
        double POS_X = 50; // Mouse Y pos
        
        // Fleet Management Data Structures
        Dictionary<string, DRONE_INFO> DRONES = new Dictionary<string, DRONE_INFO>();
        Dictionary<string, DRONE_INFO> DECENTIN_INFO = new Dictionary<string, DRONE_INFO>();
        Dictionary<string, DRONE_INFO> DOCKPOINTS = new Dictionary<string, DRONE_INFO>();
        
        // UI State Variables
        List<string> SELECTED_SQDS = new List<string>();
        List<string> SELECTED_SQDS_LOG = new List<string>();
        Vector2D STARTCLICK = new Vector2D(0, 0);
        Vector2D CLICKPOS = new Vector2D(0, 0);
        bool ISLEFT_CLICKING = false;
        bool ISRIGHT_CLICKING = false;
        bool PREV_CLICK = false;
        int CLICK_TIMER = 0;
        int TIMER = 0;
        
        // Map Control Variables
        double UI_SCALE = 100;
        double OFFSETX = 89;  // Adjusted for wide screen center
        double OFFSETY = 50;
        string FR_HOVER = "999";
        string EN_HOVER = "999";
        
        // Display and Control Blocks
        IMyTextPanel DISPLAY_PANEL;
        IMyProgrammableBlock COMMAND_MODULE;
        IMyShipController CONTROL;
        List<IMyGyro> GYROS = new List<IMyGyro>();
        
        // Cursor and Text Systems
        CURSOR SYST_CURSOR = new CURSOR();
        LETTERING SYST_LETTERING = new LETTERING();
        SYMBOLS SYST_SYMBLS = new SYMBOLS();
        char MOUSE_SYMB;
        char[,] TEXT_OUT;
        #endregion

        #region Data Structures
        class DRONE_INFO
        {
            public string ID;
            public Vector3D LOC;
            public Vector3D VELOCITY;
            public string COMLOC;
            public Vector2D UIPOS;
            public double HEALTH;
            public double SIZE;
            public double ST_SIZE;
            public string ISDOCKED;
            public Vector3D POSITION;
        }
        #endregion

        #region Constructor and Main
        public Program()
        {
            // Set up IGC listeners for fleet communication
            IGCListener = IGC.RegisterBroadcastListener(IGCTagIN);
            
            // Set script to run every 10 ticks for better performance
            Runtime.UpdateFrequency = UpdateFrequency.Update10;
            
            // Initialize UI
            UIINIT();
        }

        public void Main(string argument, UpdateType updateSource)
        {
            try
            {
                // Process IGC messages
                while (IGCListener.HasPendingMessage)
                {
                    var message = IGCListener.AcceptMessage();
                    PROCESS_FLEET_DATA(message.Data.ToString());
                }
                
                // Handle manual commands
                if (!string.IsNullOrEmpty(argument))
                {
                    PROCESS_FLEET_DATA(argument);
                }
                
                // Main display update logic
                MAIN_DISPLAY_UPDATE();
            }
            catch (Exception e)
            {
                Echo($"Error in Main: {e}");
            }
        }
        #endregion

        #region Fleet Data Processing
        void PROCESS_FLEET_DATA(string data)
        {
            try
            {
                // Process incoming fleet data messages
                // This handles communication from drone ships
                if (string.IsNullOrEmpty(data)) return;

                // Parse and update fleet information
                // Implementation depends on your specific data format
                Echo($"Received fleet data: {data}");
            }
            catch (Exception e)
            {
                Echo($"Error processing fleet data: {e}");
            }
        }
        #endregion

        #region Main Display Update
        void MAIN_DISPLAY_UPDATE()
        {
            try
            {
                // Check if required blocks are available
                if (DISPLAY_PANEL == null)
                {
                    Echo("No RFC_PANEL_WIDE Found - Please place a Wide LCD Panel with this name");
                    return;
                }

                // Draw background elements
                if (TIMER == 0) { DRAW_CHART(); }

                // Handle mouse input from ship controls
                foreach (var item in GYROS)
                { item.GyroOverride = false; }

                if (CONTROL != null && CONTROL.IsUnderControl)
                {
                    // Set gyros to avoid spinning ship while under control
                    foreach (var item in GYROS)
                    { item.GyroOverride = true; }

                    // Generate mouse position (adjusted for wide screen)
                    POS_Y = MathHelper.Clamp(POS_Y + (CONTROL.RotationIndicator.X) * 0.1, 0, COLUMNS_CT - 1);
                    POS_X = MathHelper.Clamp(POS_X + (CONTROL.RotationIndicator.Y) * 0.1, 0, ROWS_CT - 1);

                    // Clamp values
                    MathHelper.Clamp(POS_Y, 0, COLUMNS_CT - 1);
                    MathHelper.Clamp(POS_X, 0, ROWS_CT - 1);

                    // Click detection
                    ISLEFT_CLICKING = CONTROL.RollIndicator < 0;
                    ISRIGHT_CLICKING = CONTROL.RollIndicator > 0;
                }

                // Set default cursor and text
                MOUSE_SYMB = SYST_CURSOR.Cursor;
                TEXT_OUT = SYST_LETTERING.SELECT;

                // Draw fleet elements
                if (TIMER == 0)
                {
                    foreach (var item in DECENTIN_INFO) // Draw detected entities
                    { WRT_DEI(item.Value); }

                    var KEYS = new List<string>(DRONES.Keys);
                    for (int i = 0; i < DRONES.Count; i++) // Draw squadrons
                    { DRONES[KEYS[i]] = WRT_SQD(DRONES[KEYS[i]]); }
                }

                // Handle map zoom and scroll
                MAP_ZOOM_AND_SCROLL();

                // Handle squad selection and commands
                SQUAD_SELECTION();
                SQUAD_COMMANDS(ISRIGHT_CLICKING, ISLEFT_CLICKING);

                // Draw click indicator
                if (CLICKPOS.X > 4 && CLICKPOS.X < COLUMNS_CT - 4 && CLICKPOS.Y > 4 && CLICKPOS.Y < 96)
                {
                    if (CLICK_TIMER > 30)
                    {
                        DRAW[(int)CLICKPOS.Y + 2, (int)CLICKPOS.X] = green;
                        DRAW[(int)CLICKPOS.Y - 2, (int)CLICKPOS.X] = green;
                        DRAW[(int)CLICKPOS.Y, (int)CLICKPOS.X - 2] = green;
                        DRAW[(int)CLICKPOS.Y, (int)CLICKPOS.X + 2] = green;
                    }
                    if (CLICK_TIMER > 0)
                    {
                        DRAW[(int)CLICKPOS.Y + 1, (int)CLICKPOS.X] = green;
                        DRAW[(int)CLICKPOS.Y - 1, (int)CLICKPOS.X] = green;
                        DRAW[(int)CLICKPOS.Y, (int)CLICKPOS.X - 1] = green;
                        DRAW[(int)CLICKPOS.Y, (int)CLICKPOS.X + 1] = green;
                    }
                }
                if (CLICK_TIMER > 0)
                { CLICK_TIMER--; }

                // Draw current position indicator
                Vector2D ME_LOC_LO = new Vector2D(OFFSETX, OFFSETY);
                RASTER(ref ME_LOC_LO);
                if (ME_LOC_LO.X > 0 && ME_LOC_LO.X < COLUMNS_CT && ME_LOC_LO.Y > 10 && ME_LOC_LO.Y < 80)
                {
                    DRAW[(int)ME_LOC_LO.Y, (int)ME_LOC_LO.X] = blue;
                    DRAW[(int)ME_LOC_LO.Y + 1, (int)ME_LOC_LO.X] = blue;
                    DRAW[(int)ME_LOC_LO.Y - 1, (int)ME_LOC_LO.X] = blue;
                    DRAW[(int)ME_LOC_LO.Y, (int)ME_LOC_LO.X - 1] = blue;
                    DRAW[(int)ME_LOC_LO.Y, (int)ME_LOC_LO.X + 1] = blue;
                }

                // Render to LCD (optimized for wide screen)
                RENDER_TO_WIDE_LCD();
            }
            catch (Exception e)
            {
                Echo($"Error in display update: {e}");
            }
        }
        #endregion

        #region Wide LCD Rendering
        void RENDER_TO_WIDE_LCD()
        {
            try
            {
                // Text Writer (Writes to String) - optimized for wide LCD
                for (int j = TIMER * 50; j < TIMER * 50 + 50; j++)
                {
                    for (int i = 0; i < COLUMNS_CT; i++)
                    { ALLOC[j * COLUMNS_CT + (j + 1) + i] = DRAW[j, i]; }
                    ALLOC[j * (COLUMNS_CT + 1)] = '\n';
                }

                Random rand = new Random();
                ALLOC[0] = (char)rand.Next(0, 9);
                string VISUALDATA = new string(ALLOC);

                if (TIMER < 1)
                { TIMER++; return; }

                // Write to Wide LCD Panel
                VISUALDATA = VISUALDATA.Replace(" ", " " + '\uE073' + '\uE072');
                DISPLAY_PANEL.WritePublicText(VISUALDATA);
                DRAW = BDRAW.Clone() as char[,]; // Reassign after writing
                DISPLAY_PANEL.ShowPublicTextOnScreen();

                // Adjust font size for wide LCD (smaller to fit more content)
                DISPLAY_PANEL.SetValue("FontSize", 0.097f); // Adjusted for 178 pixel width
                DISPLAY_PANEL.SetValue<long>("Font", 1147350002); // Sets as monospace
                TIMER = 0;
            }
            catch (Exception e)
            {
                Echo($"Error rendering to wide LCD: {e}");
            }
        }
        #endregion

        #region Drawing Functions
        void DRAW_CHART()
        {
            // Generate map scale
            int TENS = (int)Math.Floor(UI_SCALE / 200.0);
            int UNITS = (int)Math.Floor((UI_SCALE - TENS * 200) / 10.0);

            // Calculate grid spacing for wide screen
            double SCALE = UI_SCALE / 4;
            double UNITSX = OFFSETX;
            double UNITSY = OFFSETY;

            // Adjusted grid lines for wide screen (more vertical lines)
            double X_LINE1 = -SCALE * 2;
            double X_LINE2 = -SCALE;
            double X_LINE3 = 0;
            double X_LINE4 = SCALE;
            double X_LINE5 = SCALE * 2;
            double X_LINE6 = SCALE * 3; // Additional lines for wide screen

            double Y_LINE1 = 0;
            double Y_LINE2 = SCALE;
            double Y_LINE3 = SCALE * 2;

            // Draw vertical grid lines (more for wide screen)
            DrawGridLine(new Vector2D(X_LINE1 + UNITSX, ROW_UI_START), new Vector2D(X_LINE1 + UNITSX, ROW_UI_END));
            DrawGridLine(new Vector2D(X_LINE2 + UNITSX, ROW_UI_START), new Vector2D(X_LINE2 + UNITSX, ROW_UI_END));
            DrawGridLine(new Vector2D(X_LINE3 + UNITSX, ROW_UI_START), new Vector2D(X_LINE3 + UNITSX, ROW_UI_END));
            DrawGridLine(new Vector2D(X_LINE4 + UNITSX, ROW_UI_START), new Vector2D(X_LINE4 + UNITSX, ROW_UI_END));
            DrawGridLine(new Vector2D(X_LINE5 + UNITSX, ROW_UI_START), new Vector2D(X_LINE5 + UNITSX, ROW_UI_END));
            DrawGridLine(new Vector2D(X_LINE6 + UNITSX, ROW_UI_START), new Vector2D(X_LINE6 + UNITSX, ROW_UI_END));

            // Draw horizontal grid lines
            DrawGridLine(new Vector2D(-15, UNITSY + Y_LINE1), new Vector2D(COLUMNS_CT + 15, UNITSY + Y_LINE1));
            DrawGridLine(new Vector2D(-15, UNITSY + Y_LINE2), new Vector2D(COLUMNS_CT + 15, UNITSY + Y_LINE2));
            DrawGridLine(new Vector2D(-15, UNITSY + Y_LINE3), new Vector2D(COLUMNS_CT + 15, UNITSY + Y_LINE3));
        }

        void DrawGridLine(Vector2D start, Vector2D end)
        {
            Vector2D rasteredStart = start;
            Vector2D rasteredEnd = end;
            RASTER(ref rasteredStart);
            RASTER(ref rasteredEnd);
            line(rasteredStart, rasteredEnd, mediumGray);
        }

        DRONE_INFO WRT_SQD(DRONE_INFO SHIP)
        {
            try
            {
                // Calculate display position
                Vector3D MEPOS = Me.GetPosition();
                Vector3D MEPRIGHT = Me.WorldMatrix.Right;
                Vector3D MEPOSUP = Me.WorldMatrix.Up;
                Vector3D MEPDOWN = -Me.WorldMatrix.Forward;

                // Position calculations
                Vector3D RELATIVE_POS = SHIP.LOC - MEPOS;
                double X_ORD = Vector3D.Dot(RELATIVE_POS, MEPRIGHT);
                double Y_ORD = Vector3D.Dot(RELATIVE_POS, MEPDOWN);
                double Z_ORD = Vector3D.Dot(RELATIVE_POS, MEPOSUP);

                // Convert to screen coordinates
                Vector2D RASTERED_BASE = new Vector2D(X_ORD / UI_SCALE + OFFSETX, Y_ORD / UI_SCALE + OFFSETY);
                RASTER(ref RASTERED_BASE);

                int BASEX = (int)RASTERED_BASE.X;
                int BASEY = (int)RASTERED_BASE.Y;
                int BASEZ = (int)Math.Round(BASEY - Z_ORD);
                Vector2D RASTERED_SQUAD = new Vector2D(BASEX, BASEZ);

                // Draw symbol
                SHIP.UIPOS = new Vector2D(-100, -100);
                if (BASEZ > 4 && BASEZ < 95 && BASEX > 0 && BASEX < COLUMNS_CT - 5)
                {
                    SHIP.UIPOS = new Vector2D(BASEX, BASEZ);

                    // Determine display color based on ship type
                    char DISPLAY_COLOUR = lightBlue;
                    if (SHIP.ID.Contains("I")) DISPLAY_COLOUR = cyan;
                    else if (SHIP.ID.Contains("F")) DISPLAY_COLOUR = yellow;
                    else if (SHIP.ID.Contains("C")) DISPLAY_COLOUR = orange;

                    int SIZE = 50; // Default size
                    DRAW_UI_SHP(RASTERED_BASE, RASTERED_SQUAD, DISPLAY_COLOUR, SIZE, lightGray, SHIP.ID.Substring(0, 2));
                }

                // Draw health bar if selected or hovered
                if ((Math.Abs(POS_X - (BASEX)) < 2 && Math.Abs(POS_Y - (BASEZ)) < 2) || SELECTED_SQDS.Contains(SHIP.ID))
                {
                    double HEALTH = SHIP.HEALTH;
                    for (int i = 0; i < 7; i++)
                    {
                        char VAL = (HEALTH * 7 < i) ? red : green;
                        if (BASEZ - 2 >= 0 && BASEZ - 2 < ROWS_CT && BASEX - 3 + i >= 0 && BASEX - 3 + i < COLUMNS_CT)
                            DRAW[BASEZ - 2, BASEX - 3 + i] = VAL;
                    }
                    MOUSE_SYMB = SYST_CURSOR.Select;
                }
            }
            catch (Exception e)
            {
                Echo("Error During Squadron Drawer: " + e);
            }
            return SHIP;
        }

        void WRT_DEI(DRONE_INFO SQUAD)
        {
            try
            {
                // Similar to WRT_SQD but for detected enemy entities
                Vector3D MEPOS = Me.GetPosition();
                Vector3D MEPRIGHT = Me.WorldMatrix.Right;
                Vector3D MEPOSUP = Me.WorldMatrix.Up;
                Vector3D MEPDOWN = -Me.WorldMatrix.Forward;

                Vector3D RELATIVE_POS = SQUAD.POSITION - MEPOS;
                double X_ORD = Vector3D.Dot(RELATIVE_POS, MEPRIGHT);
                double Y_ORD = Vector3D.Dot(RELATIVE_POS, MEPDOWN);
                double Z_ORD = Vector3D.Dot(RELATIVE_POS, MEPOSUP);

                Vector2D RASTERED_BASE = new Vector2D(X_ORD / UI_SCALE + OFFSETX, Y_ORD / UI_SCALE + OFFSETY);
                RASTER(ref RASTERED_BASE);

                int BASEX = (int)RASTERED_BASE.X;
                int BASEY = (int)RASTERED_BASE.Y;
                int BASEZ = (int)Math.Round(BASEY - Z_ORD);
                Vector2D RASTERED_SQUAD = new Vector2D(BASEX, BASEZ);
                SQUAD.UIPOS = new Vector2D(-100, -100);

                // Draw enemy symbol
                if (BASEZ > 7 && BASEZ < 95 && BASEX > 0 && BASEX < COLUMNS_CT - 5)
                {
                    line(RASTERED_SQUAD, RASTERED_BASE, lightGray);
                    SQUAD.UIPOS = new Vector2D(BASEX, BASEZ);

                    char SYS_COLOUR = red; // Enemy color
                    int HEIGHT = MathHelper.Clamp(50 / UI_SCALE, 1, 10);
                    int LENGTH = MathHelper.Clamp(SQUAD.ST_SIZE / UI_SCALE, 1, 30);

                    for (int j = 0; j < HEIGHT; j++)
                    {
                        for (int i = 0; i < LENGTH; i++)
                        {
                            int drawY = BASEZ - HEIGHT / 2 + j;
                            int drawX = BASEX - LENGTH / 2 + i;
                            if (drawY >= 0 && drawY < ROWS_CT && drawX >= 0 && drawX < COLUMNS_CT)
                                DRAW[drawY, drawX] = SYS_COLOUR;
                        }
                    }
                }

                // Draw enemy health bar
                if ((Math.Abs(POS_X - (BASEX)) < 4 && Math.Abs(POS_Y - (BASEZ)) < 4))
                {
                    for (int i = 0; i < 11; i++)
                    {
                        char VAL = ((SQUAD.ST_SIZE / SQUAD.SIZE) * 11 < i) ? red : green;
                        if (BASEZ - 3 >= 0 && BASEZ - 3 < ROWS_CT && BASEX - 5 + i >= 0 && BASEX - 5 + i < COLUMNS_CT)
                            DRAW[BASEZ - 3, BASEX - 5 + i] = VAL;
                    }
                }
            }
            catch (Exception e)
            {
                Echo("Error During DEI Drawer: " + e);
            }
        }
        #endregion

        #region Map Controls and UI Functions
        void MAP_ZOOM_AND_SCROLL()
        {
            // Map Zoom Controls (positioned for wide screen)
            if (Math.Abs(POS_X - COL_ZOOM_ICON1) < 3 && Math.Abs(POS_Y - ROW_ZOOM_ICON) < 4)
            {
                MOUSE_SYMB = SYST_CURSOR.Select;
                DRAW[ROW_ZOOM_ICON, COL_ZOOM_ICON1] = yellow;
                if (ISLEFT_CLICKING && PREV_CLICK == false)
                {
                    UI_SCALE = MathHelper.Clamp(UI_SCALE + 10, 10, 990);
                    OFFSETX = COLUMNS_CT / 2;
                    OFFSETY = ROWS_CT / 2;
                }
            }
            if (Math.Abs(POS_X - COL_ZOOM_ICON2) < 3 && Math.Abs(POS_Y - ROW_ZOOM_ICON) < 4)
            {
                MOUSE_SYMB = SYST_CURSOR.Select;
                DRAW[ROW_ZOOM_ICON, COL_ZOOM_ICON2] = yellow;
                if (ISLEFT_CLICKING && PREV_CLICK == false)
                {
                    UI_SCALE = MathHelper.Clamp(UI_SCALE - 10, 10, 990);
                    OFFSETX = COLUMNS_CT / 2;
                    OFFSETY = ROWS_CT / 2;
                }
            }

            // Mouse scroll converter (adjusted for wide screen)
            if ((POS_X < 1))
            { OFFSETX = OFFSETX + 1; CLICKPOS.X++; }
            if ((POS_X == COLUMNS_CT - 1))
            { OFFSETX = OFFSETX - 1; CLICKPOS.X--; }

            if ((POS_Y < 1))
            { OFFSETY = OFFSETY + 1; CLICKPOS.Y++; }
            if ((POS_Y == ROWS_CT - 1))
            { OFFSETY = OFFSETY - 1; CLICKPOS.Y--; }
        }

        void SQUAD_SELECTION()
        {
            // Handle squad selection with mouse
            if (ISLEFT_CLICKING && PREV_CLICK == false)
            {
                STARTCLICK = new Vector2D(POS_X, POS_Y);
            }

            if (ISLEFT_CLICKING && PREV_CLICK == true)
            {
                // Draw selection box
                line(STARTCLICK, new Vector2D(POS_X, STARTCLICK.Y), green);
                line(STARTCLICK, new Vector2D(STARTCLICK.X, POS_Y), green);
                line(new Vector2D(STARTCLICK.X, POS_Y), new Vector2D(POS_X, POS_Y), green);
                line(new Vector2D(POS_X, STARTCLICK.Y), new Vector2D(POS_X, POS_Y), green);

                // Add to currently selected squads
                SELECTED_SQDS = new List<string>();
                foreach (var item in DRONES)
                {
                    if (item.Value.UIPOS.X > Math.Min(STARTCLICK.X, POS_X) &&
                        item.Value.UIPOS.X < Math.Max(STARTCLICK.X, POS_X) &&
                        item.Value.UIPOS.Y > Math.Min(STARTCLICK.Y, POS_Y) &&
                        item.Value.UIPOS.Y < Math.Max(STARTCLICK.Y, POS_Y))
                    {
                        SELECTED_SQDS.Add(item.Key);
                    }
                }
            }

            PREV_CLICK = ISLEFT_CLICKING;
        }

        void SQUAD_COMMANDS(bool ISRIGHT_CLICKING, bool ISLEFTCLICKING)
        {
            // Handle squad commands
            if (SELECTED_SQDS.Count > 0)
            {
                MOUSE_SYMB = SYST_CURSOR.Select;
                FR_HOVER = "999";

                // Check for enemy targets under cursor
                foreach (var item in DECENTIN_INFO)
                {
                    if (Math.Abs(POS_X - item.Value.UIPOS.X) < 4 && Math.Abs(POS_Y - item.Value.UIPOS.Y) < 4)
                    {
                        MOUSE_SYMB = SYST_CURSOR.Attack;
                        EN_HOVER = item.Key;
                        TEXT_OUT = SYST_LETTERING.ATTACK;
                    }
                }

                // Execute commands on right click
                if (ISRIGHT_CLICKING && PREV_CLICK == false)
                {
                    if (MOUSE_SYMB == SYST_CURSOR.Attack) // Attack command
                    {
                        CLICK_TIMER = 60;
                        CLICKPOS = new Vector2D(POS_X, POS_Y);
                        EN_HOVER = EN_HOVER.Replace("#", String.Empty);

                        for (int i = 0; i < SELECTED_SQDS.Count; i++)
                        {
                            if (DRONES.ContainsKey(SELECTED_SQDS[i]))
                                DRONES[SELECTED_SQDS[i]].COMLOC = "ATTACK^" + EN_HOVER;
                        }

                        // Send command via IGC
                        IGC.SendBroadcastMessage(IGCTagOUT, $"ATTACK^{EN_HOVER}", TransmissionDistance.TransmissionDistanceMax);
                    }
                    else // Move command
                    {
                        CLICK_TIMER = 60;
                        CLICKPOS = new Vector2D(POS_X, POS_Y);

                        // Calculate world position from screen coordinates
                        Vector3D MEPOS = Me.GetPosition();
                        Vector3D MEPRIGHT = Me.WorldMatrix.Right;
                        Vector3D MEPDOWN = -Me.WorldMatrix.Forward;

                        double X_WORLD = (POS_X - OFFSETX) * UI_SCALE;
                        double Y_WORLD = (POS_Y - OFFSETY) * UI_SCALE;
                        Vector3D GOPOS = MEPOS + MEPRIGHT * X_WORLD + MEPDOWN * Y_WORLD;

                        for (int i = 0; i < SELECTED_SQDS.Count; i++)
                        {
                            if (DRONES.ContainsKey(SELECTED_SQDS[i]))
                                DRONES[SELECTED_SQDS[i]].COMLOC = "GOTO^" + Vector3D.Round(GOPOS, 2);
                        }

                        // Send command via IGC
                        IGC.SendBroadcastMessage(IGCTagOUT, $"GOTO^{Vector3D.Round(GOPOS, 2)}", TransmissionDistance.TransmissionDistanceMax);
                    }
                }
            }
        }

        void UIINIT()
        {
            // Initialize required blocks for wide LCD
            DISPLAY_PANEL = GridTerminalSystem.GetBlockWithName("RFC_PANEL_WIDE") as IMyTextPanel;
            COMMAND_MODULE = GridTerminalSystem.GetBlockWithName("CENTRAL_COMMAND") as IMyProgrammableBlock;

            try
            {
                List<IMyTerminalBlock> TEMP_RC = new List<IMyTerminalBlock>();
                GridTerminalSystem.GetBlocksOfType<IMyShipController>(TEMP_RC, b => b.CubeGrid == Me.CubeGrid && b.CustomName == "RFC_RC");
                CONTROL = TEMP_RC[0] as IMyShipController;
            }
            catch { }

            GridTerminalSystem.GetBlocksOfType<IMyGyro>(GYROS, b => b.CubeGrid == Me.CubeGrid);

            // Initialize background for wide LCD
            string BCKGRD_RAW = Me.CustomData;
            string BCKGRD = BCKGRD_RAW.Replace("\n", String.Empty).Replace(" ", String.Empty);

            // Initialize background array for wide screen
            for (int j = 0; j < ROWS_CT; j++)
            {
                for (int i = 0; i < COLUMNS_CT; i++)
                {
                    // Use default background or load from custom data if available
                    char ITEM = (BCKGRD.Length > (j * COLUMNS_CT) + i && BCKGRD[(j * COLUMNS_CT) + i] == '0') ? L1 : B;
                    BDRAW[j, i] = ITEM;
                }
            }
        }
        #endregion

        #region Utility Functions
        public void DRAW_UI_SHP(Vector2D BASE, Vector2D TOP, char PRI_COL, int SIZE, char LINE_COLOUR, string SYMBOL)
        {
            // Draw symbol line faintly
            line(TOP, BASE, LINE_COLOUR);

            // Draw symbol core
            int HEIGHT = MathHelper.Clamp(SIZE / UI_SCALE / 2, 1, 20);
            int LENGTH = MathHelper.Clamp(SIZE / UI_SCALE, 3, 20);

            for (int j = 0; j < HEIGHT; j++)
            {
                for (int i = 0; i < LENGTH; i++)
                {
                    int y = (int)TOP.Y - HEIGHT / 2 + j;
                    int x = (int)TOP.X - LENGTH / 2 + i;
                    if (x < COLUMNS_CT && y < ROWS_CT && y > ROW_UI_START && y < ROW_UI_END && x > -1 && DRAW[y, x] != P)
                    { DRAW[y, x] = PRI_COL; }
                }
            }

            // Draw symbol identifier
            WRT_SYMB(SYST_SYMBLS.SYST_SYMBLS_PROCEDURAL[SYMBOL], (int)TOP.Y - 1, (int)TOP.X - 3);
        }

        public void line(Vector2D IN_COORD, Vector2D OUT_COORD, char color)
        {
            // Bresenham's line algorithm adapted for wide screen
            int x = (int)IN_COORD.X;
            int y = (int)IN_COORD.Y;
            int w = (int)OUT_COORD.X - x;
            int h = (int)OUT_COORD.Y - y;
            int dx1 = 0, dy1 = 0, dx2 = 0, dy2 = 0;

            if (w < 0) dx1 = -1; else if (w > 0) dx1 = 1;
            if (h < 0) dy1 = -1; else if (h > 0) dy1 = 1;
            if (w < 0) dx2 = -1; else if (w > 0) dx2 = 1;

            int longest = Math.Abs(w);
            int shortest = Math.Abs(h);

            if (!(longest > shortest))
            {
                longest = Math.Abs(h);
                shortest = Math.Abs(w);
                if (h < 0) dy2 = -1; else if (h > 0) dy2 = 1;
                dx2 = 0;
            }

            int numerator = longest >> 1;
            for (int i = 0; i <= longest; i++)
            {
                if (!(x < COLUMNS_CT && y < ROW_UI_END)) { return; }
                if (y > ROW_UI_START && x > -1 && x < COLUMNS_CT && y < ROWS_CT && DRAW[y, x] != P && DRAW[y, x] != red && DRAW[y, x] != green)
                { DRAW[y, x] = color; }

                numerator += shortest;
                if (!(numerator < longest))
                {
                    numerator -= longest;
                    x += dx1;
                    y += dy1;
                }
                else
                {
                    x += dx2;
                    y += dy2;
                }
            }
        }

        public void WRT_SYMB(char[,] SYMBOL, int START_ROW, int START_COL)
        {
            // Write symbol at given coordinates (bounds checking for wide screen)
            for (int j = START_ROW; j < START_ROW + SYMBOL.GetLength(0); j++)
            {
                for (int i = START_COL; i < START_COL + SYMBOL.GetLength(1); i++)
                {
                    if (j > ROWS_CT - 1 || i > COLUMNS_CT - 1 || i < 0 || j < 0) { continue; }
                    if (SYMBOL[j - START_ROW, i - START_COL] == B || DRAW[j, i] == green) { continue; }
                    DRAW[j, i] = SYMBOL[j - START_ROW, i - START_COL];
                }
            }
        }

        public void RASTER(ref Vector2D COORD)
        {
            // Convert world coordinates to screen coordinates
            COORD = new Vector2D(MathHelper.Clamp(COORD.X, 0, COLUMNS_CT - 1), MathHelper.Clamp(COORD.Y, 0, ROWS_CT - 1));
        }
        #endregion

        #region Support Classes
        class CURSOR
        {
            public char Cursor = '+';
            public char Select = 'o';
            public char Attack = 'x';
            public char Dock = 'd';
        }

        class LETTERING
        {
            public char[,] SELECT = new char[,] { { 'S', 'E', 'L' } };
            public char[,] ATTACK = new char[,] { { 'A', 'T', 'K' } };
            public char[,] DOCK = new char[,] { { 'D', 'O', 'K' } };
        }

        class SYMBOLS
        {
            public Dictionary<string, char[,]> SYST_SYMBLS_PROCEDURAL = new Dictionary<string, char[,]>()
            {
                { "I", new char[,] { { 'I' } } },
                { "F", new char[,] { { 'F' } } },
                { "C", new char[,] { { 'C' } } },
                { "U", new char[,] { { 'U' } } },
                { "H", new char[,] { { 'H' } } }
            };
        }
        #endregion
    }
}
