<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>netframework48</TargetFramework>
    <RootNamespace>IngameScript</RootNamespace>
    <LangVersion>6</LangVersion>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <Configurations>Release;Debug</Configurations>
    <Platforms>x64</Platforms>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Mal.Mdk2.PbAnalyzers" Version="2.1.13">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Mal.Mdk2.PbPackager" Version="2.1.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Mal.Mdk2.References" Version="2.2.4" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="Instructions.readme" />
    <AdditionalFiles Include="Instructions.readme" />
    <AdditionalFiles Include="thumb.png" />
  </ItemGroup>
</Project>