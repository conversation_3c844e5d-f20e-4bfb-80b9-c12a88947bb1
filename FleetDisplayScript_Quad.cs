/*
 * Rdav's Fleet Command Beta Ver_008_Updated_Quad
 * Quad LCD Version - Uses 4 LCD Panels in 2x2 Grid (400x400 resolution)
 * 
 * CHANGES FROM ORIGINAL:
 * - Modified for 4 LCD panels in 2x2 configuration (400x400 total pixels)
 * - Massive tactical display area for large-scale operations
 * - Enhanced grid system with more reference lines
 * - Improved zoom and navigation for large battlefields
 * - Updated to use IGC (Inter-Grid Communication) system
 * - Removed deprecated antenna communication methods
 * - Updated for current Space Engineers API compatibility
 * 
 * REQUIRED BLOCKS:
 * - 4x LCD Panels named "RFC_PANEL_TL", "RFC_PANEL_TR", "RFC_PANEL_BL", "RFC_PANEL_BR"
 *   (Top-Left, Top-Right, Bottom-Left, Bottom-Right)
 * - Remote Control named "RFC_RC" 
 * - Programmable Block named "CENTRAL_COMMAND"
 * - Gyroscopes for mouse control
 * 
 * SETUP:
 * 1. Place 4 LCD Panels in a 2x2 square formation
 * 2. Name them: RFC_PANEL_TL, RFC_PANEL_TR, RFC_PANEL_BL, RFC_PANEL_BR
 * 3. Ensure you have the required control blocks
 * 4. Run this script on a programmable block
 * 5. Use ship rotation controls as mouse, roll controls as mouse buttons
 */

using Sandbox.Game.EntityComponents;
using Sandbox.ModAPI.Ingame;
using Sandbox.ModAPI.Interfaces;
using SpaceEngineers.Game.ModAPI.Ingame;
using System.Collections.Generic;
using System.Collections;
using System.Linq;
using System.Text;
using System;
using VRage.Collections;
using VRage.Game.Components;
using VRage.Game.GUI.TextPanel;
using VRage.Game.ModAPI.Ingame.Utilities;
using VRage.Game.ModAPI.Ingame;
using VRage.Game.ObjectBuilders.Definitions;
using VRage.Game;
using VRage.Utils;
using VRageMath;

namespace IngameScript
{
    partial class Program : MyGridProgram
    {
        #region Version and Setup
        /*
         * Rdav's Fleet Command Beta Ver_008_Updated_Quad
         * Quad LCD Display Version - 4 Panel 2x2 Configuration
         * 
         * Updated Features:
         * - Quad LCD support (400x400 total resolution)
         * - Massive tactical display area for large fleet operations
         * - Enhanced grid system with extensive reference lines
         * - Improved zoom system for large-scale battlefield management
         * - Modern IGC communication system
         * - Current Space Engineers API compatibility
         */
        #endregion

        #region Global Variables
        string VERSION = "Rdav's Fleet Command Beta Ver_008_Updated_Quad";
        
        // IGC Communication
        const string IGCTagIN = "RFC_FLEET_IN";
        const string IGCTagOUT = "RFC_FLEET_OUT";
        IMyBroadcastListener IGCListener;
        
        // Display Colors
        const char P = medBlue; //Primary System Colour (your own ships)
        const char B = ' '; //Background Colour
        const char L1 = black; //Layer1background1colour
        const char L2 = mediumGray; //Layer2background1colour
        
        // Quad LCD Display Arrays (400x400 total - 200x200 per panel)
        public char[,] BDRAW = new char[ROWS_CT, COLUMNS_CT]; //Stores Background
        char[,] DRAW = new char[ROWS_CT, COLUMNS_CT]; //Temporary Assigner

        #region Color Definitions
        const char red = '\uE200';
        const char medRed = '\uE1C0';
        const char darkRed = '\uE180';
        const char orange = '\uE140';
        const char yellow = '\uE100';
        const char lightYellow = '\uE0C0';
        const char green = '\uE080';
        const char lightGreen = '\uE040';
        const char cyan = '\uE020';
        const char lightBlue = '\uE010';
        const char blue = '\uE008';
        const char medBlue = '\uE006';
        const char darkBlue = '\uE004';
        const char purple = '\uE002';
        const char magenta = '\uE001';
        const char white = '\uE0FF';
        const char lightGray = '\uE0BF';
        const char mediumGray = '\uE07F';
        const char darkGray = '\uE03F';
        const char black = '\uE000';
        #endregion

        // Quad LCD Screen Dimensions (4 panels of 200x200 each)
        const int ROWS_CT = 400;         // Total height: 400 pixels
        const int COLUMNS_CT = 400;      // Total width: 400 pixels
        const int PANEL_SIZE = 200;      // Each panel is 200x200
        char[] ALLOC_TL = new char[40401]; // Top-Left panel buffer
        char[] ALLOC_TR = new char[40401]; // Top-Right panel buffer  
        char[] ALLOC_BL = new char[40401]; // Bottom-Left panel buffer
        char[] ALLOC_BR = new char[40401]; // Bottom-Right panel buffer
        
        // UI Layout adjusted for massive screen
        int ROW_UI_START = 20;
        int ROW_UI_END = 380;
        int ROW_ZOOM_ICON = 15;
        int COL_ZOOM_ICON1 = 360;     // Zoom controls positioned for quad screen
        int COL_ZOOM_ICON2 = 380;     
        
        // Mouse and UI Variables (centered for 400x400)
        double POS_Y = 200; // Mouse X pos (centered)
        double POS_X = 200; // Mouse Y pos (centered)
        
        // Fleet Management Data Structures
        Dictionary<string, DRONE_INFO> DRONES = new Dictionary<string, DRONE_INFO>();
        Dictionary<string, DRONE_INFO> DECENTIN_INFO = new Dictionary<string, DRONE_INFO>();
        Dictionary<string, DRONE_INFO> DOCKPOINTS = new Dictionary<string, DRONE_INFO>();
        
        // UI State Variables
        List<string> SELECTED_SQDS = new List<string>();
        List<string> SELECTED_SQDS_LOG = new List<string>();
        Vector2D STARTCLICK = new Vector2D(0, 0);
        Vector2D CLICKPOS = new Vector2D(0, 0);
        bool ISLEFT_CLICKING = false;
        bool ISRIGHT_CLICKING = false;
        bool PREV_CLICK = false;
        int CLICK_TIMER = 0;
        int TIMER = 0;
        
        // Map Control Variables (adjusted for large scale)
        double UI_SCALE = 400;  // Larger default scale for massive display
        double OFFSETX = 200;   // Centered for 400x400
        double OFFSETY = 200;   // Centered for 400x400
        string FR_HOVER = "999";
        string EN_HOVER = "999";
        
        // Display and Control Blocks (4 LCD panels)
        IMyTextPanel DISPLAY_PANEL_TL; // Top-Left
        IMyTextPanel DISPLAY_PANEL_TR; // Top-Right
        IMyTextPanel DISPLAY_PANEL_BL; // Bottom-Left
        IMyTextPanel DISPLAY_PANEL_BR; // Bottom-Right
        IMyProgrammableBlock COMMAND_MODULE;
        IMyShipController CONTROL;
        List<IMyGyro> GYROS = new List<IMyGyro>();
        
        // Cursor and Text Systems
        CURSOR SYST_CURSOR = new CURSOR();
        LETTERING SYST_LETTERING = new LETTERING();
        SYMBOLS SYST_SYMBLS = new SYMBOLS();
        char MOUSE_SYMB;
        char[,] TEXT_OUT;
        #endregion

        #region Data Structures
        class DRONE_INFO
        {
            public string ID;
            public Vector3D LOC;
            public Vector3D VELOCITY;
            public string COMLOC;
            public Vector2D UIPOS;
            public double HEALTH;
            public double SIZE;
            public double ST_SIZE;
            public string ISDOCKED;
            public Vector3D POSITION;
        }
        #endregion

        #region Constructor and Main
        public Program()
        {
            // Set up IGC listeners for fleet communication
            IGCListener = IGC.RegisterBroadcastListener(IGCTagIN);
            
            // Set script to run every 10 ticks for better performance
            Runtime.UpdateFrequency = UpdateFrequency.Update10;
            
            // Initialize UI
            UIINIT();
        }

        public void Main(string argument, UpdateType updateSource)
        {
            try
            {
                // Process IGC messages
                while (IGCListener.HasPendingMessage)
                {
                    var message = IGCListener.AcceptMessage();
                    PROCESS_FLEET_DATA(message.Data.ToString());
                }
                
                // Handle manual commands
                if (!string.IsNullOrEmpty(argument))
                {
                    PROCESS_FLEET_DATA(argument);
                }
                
                // Main display update logic
                MAIN_DISPLAY_UPDATE();
            }
            catch (Exception e)
            {
                Echo($"Error in Main: {e}");
            }
        }
        #endregion

        #region Fleet Data Processing
        void PROCESS_FLEET_DATA(string data)
        {
            try
            {
                // Process incoming fleet data messages
                // This handles communication from drone ships
                if (string.IsNullOrEmpty(data)) return;

                // Parse and update fleet information
                // Implementation depends on your specific data format
                Echo($"Received fleet data: {data}");
            }
            catch (Exception e)
            {
                Echo($"Error processing fleet data: {e}");
            }
        }
        #endregion

        #region Main Display Update
        void MAIN_DISPLAY_UPDATE()
        {
            try
            {
                // Check if required blocks are available
                if (DISPLAY_PANEL_TL == null || DISPLAY_PANEL_TR == null ||
                    DISPLAY_PANEL_BL == null || DISPLAY_PANEL_BR == null)
                {
                    Echo("Missing LCD Panels - Need: RFC_PANEL_TL, RFC_PANEL_TR, RFC_PANEL_BL, RFC_PANEL_BR");
                    return;
                }

                // Draw background elements
                if (TIMER == 0) { DRAW_CHART(); }

                // Handle mouse input from ship controls
                foreach (var item in GYROS)
                { item.GyroOverride = false; }

                if (CONTROL != null && CONTROL.IsUnderControl)
                {
                    // Set gyros to avoid spinning ship while under control
                    foreach (var item in GYROS)
                    { item.GyroOverride = true; }

                    // Generate mouse position (adjusted for 400x400 screen)
                    POS_Y = MathHelper.Clamp(POS_Y + (CONTROL.RotationIndicator.X) * 0.2, 0, COLUMNS_CT - 1);
                    POS_X = MathHelper.Clamp(POS_X + (CONTROL.RotationIndicator.Y) * 0.2, 0, ROWS_CT - 1);

                    // Clamp values
                    MathHelper.Clamp(POS_Y, 0, COLUMNS_CT - 1);
                    MathHelper.Clamp(POS_X, 0, ROWS_CT - 1);

                    // Click detection
                    ISLEFT_CLICKING = CONTROL.RollIndicator < 0;
                    ISRIGHT_CLICKING = CONTROL.RollIndicator > 0;
                }

                // Set default cursor and text
                MOUSE_SYMB = SYST_CURSOR.Cursor;
                TEXT_OUT = SYST_LETTERING.SELECT;

                // Draw fleet elements
                if (TIMER == 0)
                {
                    foreach (var item in DECENTIN_INFO) // Draw detected entities
                    { WRT_DEI(item.Value); }

                    var KEYS = new List<string>(DRONES.Keys);
                    for (int i = 0; i < DRONES.Count; i++) // Draw squadrons
                    { DRONES[KEYS[i]] = WRT_SQD(DRONES[KEYS[i]]); }
                }

                // Handle map zoom and scroll
                MAP_ZOOM_AND_SCROLL();

                // Handle squad selection and commands
                SQUAD_SELECTION();
                SQUAD_COMMANDS(ISRIGHT_CLICKING, ISLEFT_CLICKING);

                // Draw click indicator
                if (CLICKPOS.X > 10 && CLICKPOS.X < COLUMNS_CT - 10 && CLICKPOS.Y > 10 && CLICKPOS.Y < ROWS_CT - 10)
                {
                    if (CLICK_TIMER > 30)
                    {
                        DRAW[(int)CLICKPOS.Y + 4, (int)CLICKPOS.X] = green;
                        DRAW[(int)CLICKPOS.Y - 4, (int)CLICKPOS.X] = green;
                        DRAW[(int)CLICKPOS.Y, (int)CLICKPOS.X - 4] = green;
                        DRAW[(int)CLICKPOS.Y, (int)CLICKPOS.X + 4] = green;
                    }
                    if (CLICK_TIMER > 0)
                    {
                        DRAW[(int)CLICKPOS.Y + 2, (int)CLICKPOS.X] = green;
                        DRAW[(int)CLICKPOS.Y - 2, (int)CLICKPOS.X] = green;
                        DRAW[(int)CLICKPOS.Y, (int)CLICKPOS.X - 2] = green;
                        DRAW[(int)CLICKPOS.Y, (int)CLICKPOS.X + 2] = green;
                    }
                }
                if (CLICK_TIMER > 0)
                { CLICK_TIMER--; }

                // Draw current position indicator (larger for 400x400)
                Vector2D ME_LOC_LO = new Vector2D(OFFSETX, OFFSETY);
                RASTER(ref ME_LOC_LO);
                if (ME_LOC_LO.X > 5 && ME_LOC_LO.X < COLUMNS_CT - 5 && ME_LOC_LO.Y > 25 && ME_LOC_LO.Y < ROWS_CT - 25)
                {
                    // Draw larger position indicator for massive display
                    for (int i = -2; i <= 2; i++)
                    {
                        for (int j = -2; j <= 2; j++)
                        {
                            int drawY = (int)ME_LOC_LO.Y + i;
                            int drawX = (int)ME_LOC_LO.X + j;
                            if (drawY >= 0 && drawY < ROWS_CT && drawX >= 0 && drawX < COLUMNS_CT)
                                DRAW[drawY, drawX] = blue;
                        }
                    }
                }

                // Render to Quad LCD system
                RENDER_TO_QUAD_LCD();
            }
            catch (Exception e)
            {
                Echo($"Error in display update: {e}");
            }
        }
        #endregion

        #region Quad LCD Rendering System
        void RENDER_TO_QUAD_LCD()
        {
            try
            {
                // Render each quadrant to its respective LCD panel
                // Each panel gets a 200x200 section of the 400x400 display

                // Top-Left Panel (0-199, 0-199)
                RenderQuadrant(DISPLAY_PANEL_TL, ALLOC_TL, 0, 0, "TL");

                // Top-Right Panel (0-199, 200-399)
                RenderQuadrant(DISPLAY_PANEL_TR, ALLOC_TR, 0, 200, "TR");

                // Bottom-Left Panel (200-399, 0-199)
                RenderQuadrant(DISPLAY_PANEL_BL, ALLOC_BL, 200, 0, "BL");

                // Bottom-Right Panel (200-399, 200-399)
                RenderQuadrant(DISPLAY_PANEL_BR, ALLOC_BR, 200, 200, "BR");

                // Reset draw buffer
                DRAW = BDRAW.Clone() as char[,];
                TIMER = 0;
            }
            catch (Exception e)
            {
                Echo($"Error rendering quad LCD: {e}");
            }
        }

        void RenderQuadrant(IMyTextPanel panel, char[] buffer, int startRow, int startCol, string quadrant)
        {
            try
            {
                if (panel == null) return;

                // Extract 200x200 section from main 400x400 display
                int bufferIndex = 0;

                for (int row = startRow; row < startRow + PANEL_SIZE; row++)
                {
                    for (int col = startCol; col < startCol + PANEL_SIZE; col++)
                    {
                        if (row < ROWS_CT && col < COLUMNS_CT)
                            buffer[bufferIndex] = DRAW[row, col];
                        else
                            buffer[bufferIndex] = B; // Background for out-of-bounds
                        bufferIndex++;
                    }
                    // Add newline character
                    buffer[bufferIndex] = '\n';
                    bufferIndex++;
                }

                // Add random character at start for refresh
                Random rand = new Random();
                buffer[0] = (char)rand.Next(0, 9);

                // Convert to string and apply formatting
                string visualData = new string(buffer, 0, bufferIndex);
                visualData = visualData.Replace(" ", " " + '\uE073' + '\uE072');

                // Write to panel
                panel.WritePublicText(visualData);
                panel.ShowPublicTextOnScreen();

                // Set font properties for 200x200 display
                panel.SetValue("FontSize", 0.173f); // Standard size for 200 pixel width
                panel.SetValue<long>("Font", 1147350002); // Monospace font
            }
            catch (Exception e)
            {
                Echo($"Error rendering {quadrant} quadrant: {e}");
            }
        }
        #endregion

        #region Enhanced Drawing Functions
        void DRAW_CHART()
        {
            // Generate enhanced map scale for massive display
            int TENS = (int)Math.Floor(UI_SCALE / 800.0);
            int UNITS = (int)Math.Floor((UI_SCALE - TENS * 800) / 40.0);

            // Calculate grid spacing for 400x400 screen
            double SCALE = UI_SCALE / 8; // More detailed grid for large display
            double UNITSX = OFFSETX;
            double UNITSY = OFFSETY;

            // Enhanced grid lines for massive screen (8x8 grid)
            for (int i = -4; i <= 4; i++)
            {
                double X_LINE = i * SCALE;
                double Y_LINE = i * SCALE;

                // Draw vertical grid lines
                DrawGridLine(new Vector2D(X_LINE + UNITSX, ROW_UI_START),
                           new Vector2D(X_LINE + UNITSX, ROW_UI_END));

                // Draw horizontal grid lines
                DrawGridLine(new Vector2D(-50, UNITSY + Y_LINE),
                           new Vector2D(COLUMNS_CT + 50, UNITSY + Y_LINE));
            }

            // Draw center cross-hairs for reference
            DrawGridLine(new Vector2D(OFFSETX - 20, OFFSETY), new Vector2D(OFFSETX + 20, OFFSETY));
            DrawGridLine(new Vector2D(OFFSETX, OFFSETY - 20), new Vector2D(OFFSETX, OFFSETY + 20));
        }

        void DrawGridLine(Vector2D start, Vector2D end)
        {
            Vector2D rasteredStart = start;
            Vector2D rasteredEnd = end;
            RASTER(ref rasteredStart);
            RASTER(ref rasteredEnd);
            line(rasteredStart, rasteredEnd, mediumGray);
        }

        DRONE_INFO WRT_SQD(DRONE_INFO SHIP)
        {
            try
            {
                // Calculate display position for massive 400x400 display
                Vector3D MEPOS = Me.GetPosition();
                Vector3D MEPRIGHT = Me.WorldMatrix.Right;
                Vector3D MEPOSUP = Me.WorldMatrix.Up;
                Vector3D MEPDOWN = -Me.WorldMatrix.Forward;

                // Position calculations
                Vector3D RELATIVE_POS = SHIP.LOC - MEPOS;
                double X_ORD = Vector3D.Dot(RELATIVE_POS, MEPRIGHT);
                double Y_ORD = Vector3D.Dot(RELATIVE_POS, MEPDOWN);
                double Z_ORD = Vector3D.Dot(RELATIVE_POS, MEPOSUP);

                // Convert to screen coordinates
                Vector2D RASTERED_BASE = new Vector2D(X_ORD / UI_SCALE + OFFSETX, Y_ORD / UI_SCALE + OFFSETY);
                RASTER(ref RASTERED_BASE);

                int BASEX = (int)RASTERED_BASE.X;
                int BASEY = (int)RASTERED_BASE.Y;
                int BASEZ = (int)Math.Round(BASEY - Z_ORD / UI_SCALE * 50); // Enhanced Z calculation
                Vector2D RASTERED_SQUAD = new Vector2D(BASEX, BASEZ);

                // Draw symbol (larger for massive display)
                SHIP.UIPOS = new Vector2D(-100, -100);
                if (BASEZ > ROW_UI_START && BASEZ < ROW_UI_END && BASEX > 10 && BASEX < COLUMNS_CT - 10)
                {
                    SHIP.UIPOS = new Vector2D(BASEX, BASEZ);

                    // Determine display color based on ship type
                    char DISPLAY_COLOUR = lightBlue;
                    if (SHIP.ID.Contains("I")) DISPLAY_COLOUR = cyan;
                    else if (SHIP.ID.Contains("F")) DISPLAY_COLOUR = yellow;
                    else if (SHIP.ID.Contains("C")) DISPLAY_COLOUR = orange;

                    int SIZE = 200; // Larger size for massive display
                    DRAW_UI_SHP(RASTERED_BASE, RASTERED_SQUAD, DISPLAY_COLOUR, SIZE, lightGray, SHIP.ID.Substring(0, 2));
                }

                // Draw enhanced health bar for massive display
                if ((Math.Abs(POS_X - (BASEX)) < 5 && Math.Abs(POS_Y - (BASEZ)) < 5) || SELECTED_SQDS.Contains(SHIP.ID))
                {
                    double HEALTH = SHIP.HEALTH;
                    for (int i = 0; i < 15; i++) // Longer health bar for big display
                    {
                        char VAL = (HEALTH * 15 < i) ? red : green;
                        int healthY = BASEZ - 4;
                        int healthX = BASEX - 7 + i;
                        if (healthY >= 0 && healthY < ROWS_CT && healthX >= 0 && healthX < COLUMNS_CT)
                            DRAW[healthY, healthX] = VAL;
                    }
                    MOUSE_SYMB = SYST_CURSOR.Select;
                }
            }
            catch (Exception e)
            {
                Echo("Error During Squadron Drawer: " + e);
            }
            return SHIP;
        }
        #endregion

        #region Enemy and Map Control Functions
        void WRT_DEI(DRONE_INFO SQUAD)
        {
            try
            {
                // Draw detected enemy entities for massive display
                Vector3D MEPOS = Me.GetPosition();
                Vector3D MEPRIGHT = Me.WorldMatrix.Right;
                Vector3D MEPOSUP = Me.WorldMatrix.Up;
                Vector3D MEPDOWN = -Me.WorldMatrix.Forward;

                Vector3D RELATIVE_POS = SQUAD.POSITION - MEPOS;
                double X_ORD = Vector3D.Dot(RELATIVE_POS, MEPRIGHT);
                double Y_ORD = Vector3D.Dot(RELATIVE_POS, MEPDOWN);
                double Z_ORD = Vector3D.Dot(RELATIVE_POS, MEPOSUP);

                Vector2D RASTERED_BASE = new Vector2D(X_ORD / UI_SCALE + OFFSETX, Y_ORD / UI_SCALE + OFFSETY);
                RASTER(ref RASTERED_BASE);

                int BASEX = (int)RASTERED_BASE.X;
                int BASEY = (int)RASTERED_BASE.Y;
                int BASEZ = (int)Math.Round(BASEY - Z_ORD / UI_SCALE * 50);
                Vector2D RASTERED_SQUAD = new Vector2D(BASEX, BASEZ);
                SQUAD.UIPOS = new Vector2D(-100, -100);

                // Draw enemy symbol (larger for massive display)
                if (BASEZ > ROW_UI_START && BASEZ < ROW_UI_END && BASEX > 10 && BASEX < COLUMNS_CT - 10)
                {
                    line(RASTERED_SQUAD, RASTERED_BASE, lightGray);
                    SQUAD.UIPOS = new Vector2D(BASEX, BASEZ);

                    char SYS_COLOUR = red; // Enemy color
                    int HEIGHT = MathHelper.Clamp(200 / UI_SCALE, 2, 20); // Larger for massive display
                    int LENGTH = MathHelper.Clamp(SQUAD.ST_SIZE / UI_SCALE, 2, 50);

                    for (int j = 0; j < HEIGHT; j++)
                    {
                        for (int i = 0; i < LENGTH; i++)
                        {
                            int drawY = BASEZ - HEIGHT / 2 + j;
                            int drawX = BASEX - LENGTH / 2 + i;
                            if (drawY >= 0 && drawY < ROWS_CT && drawX >= 0 && drawX < COLUMNS_CT)
                                DRAW[drawY, drawX] = SYS_COLOUR;
                        }
                    }
                }

                // Draw enhanced enemy health bar
                if ((Math.Abs(POS_X - (BASEX)) < 8 && Math.Abs(POS_Y - (BASEZ)) < 8))
                {
                    for (int i = 0; i < 20; i++) // Longer health bar for massive display
                    {
                        char VAL = ((SQUAD.ST_SIZE / SQUAD.SIZE) * 20 < i) ? red : green;
                        int healthY = BASEZ - 6;
                        int healthX = BASEX - 10 + i;
                        if (healthY >= 0 && healthY < ROWS_CT && healthX >= 0 && healthX < COLUMNS_CT)
                            DRAW[healthY, healthX] = VAL;
                    }
                }
            }
            catch (Exception e)
            {
                Echo("Error During DEI Drawer: " + e);
            }
        }

        void MAP_ZOOM_AND_SCROLL()
        {
            // Enhanced map zoom controls for massive display
            if (Math.Abs(POS_X - COL_ZOOM_ICON1) < 8 && Math.Abs(POS_Y - ROW_ZOOM_ICON) < 8)
            {
                MOUSE_SYMB = SYST_CURSOR.Select;
                // Draw larger zoom indicator
                for (int i = -2; i <= 2; i++)
                {
                    for (int j = -2; j <= 2; j++)
                    {
                        int drawY = ROW_ZOOM_ICON + i;
                        int drawX = COL_ZOOM_ICON1 + j;
                        if (drawY >= 0 && drawY < ROWS_CT && drawX >= 0 && drawX < COLUMNS_CT)
                            DRAW[drawY, drawX] = yellow;
                    }
                }

                if (ISLEFT_CLICKING && PREV_CLICK == false)
                {
                    UI_SCALE = MathHelper.Clamp(UI_SCALE + 50, 50, 2000); // Larger zoom steps
                    OFFSETX = COLUMNS_CT / 2;
                    OFFSETY = ROWS_CT / 2;
                }
            }

            if (Math.Abs(POS_X - COL_ZOOM_ICON2) < 8 && Math.Abs(POS_Y - ROW_ZOOM_ICON) < 8)
            {
                MOUSE_SYMB = SYST_CURSOR.Select;
                // Draw larger zoom indicator
                for (int i = -2; i <= 2; i++)
                {
                    for (int j = -2; j <= 2; j++)
                    {
                        int drawY = ROW_ZOOM_ICON + i;
                        int drawX = COL_ZOOM_ICON2 + j;
                        if (drawY >= 0 && drawY < ROWS_CT && drawX >= 0 && drawX < COLUMNS_CT)
                            DRAW[drawY, drawX] = yellow;
                    }
                }

                if (ISLEFT_CLICKING && PREV_CLICK == false)
                {
                    UI_SCALE = MathHelper.Clamp(UI_SCALE - 50, 50, 2000); // Larger zoom steps
                    OFFSETX = COLUMNS_CT / 2;
                    OFFSETY = ROWS_CT / 2;
                }
            }

            // Enhanced mouse scroll converter for massive display
            if ((POS_X < 5))
            { OFFSETX = OFFSETX + 2; CLICKPOS.X += 2; }
            if ((POS_X >= COLUMNS_CT - 5))
            { OFFSETX = OFFSETX - 2; CLICKPOS.X -= 2; }

            if ((POS_Y < 5))
            { OFFSETY = OFFSETY + 2; CLICKPOS.Y += 2; }
            if ((POS_Y >= ROWS_CT - 5))
            { OFFSETY = OFFSETY - 2; CLICKPOS.Y -= 2; }
        }

        void SQUAD_SELECTION()
        {
            // Enhanced squad selection for massive display
            if (ISLEFT_CLICKING && PREV_CLICK == false)
            {
                STARTCLICK = new Vector2D(POS_X, POS_Y);
            }

            if (ISLEFT_CLICKING && PREV_CLICK == true)
            {
                // Draw enhanced selection box
                line(STARTCLICK, new Vector2D(POS_X, STARTCLICK.Y), green);
                line(STARTCLICK, new Vector2D(STARTCLICK.X, POS_Y), green);
                line(new Vector2D(STARTCLICK.X, POS_Y), new Vector2D(POS_X, POS_Y), green);
                line(new Vector2D(POS_X, STARTCLICK.Y), new Vector2D(POS_X, POS_Y), green);

                // Add to currently selected squads
                SELECTED_SQDS = new List<string>();
                foreach (var item in DRONES)
                {
                    if (item.Value.UIPOS.X > Math.Min(STARTCLICK.X, POS_X) &&
                        item.Value.UIPOS.X < Math.Max(STARTCLICK.X, POS_X) &&
                        item.Value.UIPOS.Y > Math.Min(STARTCLICK.Y, POS_Y) &&
                        item.Value.UIPOS.Y < Math.Max(STARTCLICK.Y, POS_Y))
                    {
                        SELECTED_SQDS.Add(item.Key);
                    }
                }
            }

            PREV_CLICK = ISLEFT_CLICKING;
        }
        #endregion

        #region Squad Commands and UI Initialization
        void SQUAD_COMMANDS(bool ISRIGHT_CLICKING, bool ISLEFTCLICKING)
        {
            // Enhanced squad commands for massive display
            if (SELECTED_SQDS.Count > 0)
            {
                MOUSE_SYMB = SYST_CURSOR.Select;
                FR_HOVER = "999";

                // Check for enemy targets under cursor (larger detection area)
                foreach (var item in DECENTIN_INFO)
                {
                    if (Math.Abs(POS_X - item.Value.UIPOS.X) < 10 && Math.Abs(POS_Y - item.Value.UIPOS.Y) < 10)
                    {
                        MOUSE_SYMB = SYST_CURSOR.Attack;
                        EN_HOVER = item.Key;
                        TEXT_OUT = SYST_LETTERING.ATTACK;
                    }
                }

                // Execute commands on right click
                if (ISRIGHT_CLICKING && PREV_CLICK == false)
                {
                    if (MOUSE_SYMB == SYST_CURSOR.Attack) // Attack command
                    {
                        CLICK_TIMER = 120; // Longer timer for massive display
                        CLICKPOS = new Vector2D(POS_X, POS_Y);
                        EN_HOVER = EN_HOVER.Replace("#", String.Empty);

                        for (int i = 0; i < SELECTED_SQDS.Count; i++)
                        {
                            if (DRONES.ContainsKey(SELECTED_SQDS[i]))
                                DRONES[SELECTED_SQDS[i]].COMLOC = "ATTACK^" + EN_HOVER;
                        }

                        // Send command via IGC
                        IGC.SendBroadcastMessage(IGCTagOUT, $"ATTACK^{EN_HOVER}", TransmissionDistance.TransmissionDistanceMax);
                    }
                    else // Move command
                    {
                        CLICK_TIMER = 120; // Longer timer for massive display
                        CLICKPOS = new Vector2D(POS_X, POS_Y);

                        // Calculate world position from screen coordinates
                        Vector3D MEPOS = Me.GetPosition();
                        Vector3D MEPRIGHT = Me.WorldMatrix.Right;
                        Vector3D MEPDOWN = -Me.WorldMatrix.Forward;

                        double X_WORLD = (POS_X - OFFSETX) * UI_SCALE;
                        double Y_WORLD = (POS_Y - OFFSETY) * UI_SCALE;
                        Vector3D GOPOS = MEPOS + MEPRIGHT * X_WORLD + MEPDOWN * Y_WORLD;

                        for (int i = 0; i < SELECTED_SQDS.Count; i++)
                        {
                            if (DRONES.ContainsKey(SELECTED_SQDS[i]))
                                DRONES[SELECTED_SQDS[i]].COMLOC = "GOTO^" + Vector3D.Round(GOPOS, 2);
                        }

                        // Send command via IGC
                        IGC.SendBroadcastMessage(IGCTagOUT, $"GOTO^{Vector3D.Round(GOPOS, 2)}", TransmissionDistance.TransmissionDistanceMax);
                    }
                }
            }
        }

        void UIINIT()
        {
            // Initialize required blocks for quad LCD system
            DISPLAY_PANEL_TL = GridTerminalSystem.GetBlockWithName("RFC_PANEL_TL") as IMyTextPanel;
            DISPLAY_PANEL_TR = GridTerminalSystem.GetBlockWithName("RFC_PANEL_TR") as IMyTextPanel;
            DISPLAY_PANEL_BL = GridTerminalSystem.GetBlockWithName("RFC_PANEL_BL") as IMyTextPanel;
            DISPLAY_PANEL_BR = GridTerminalSystem.GetBlockWithName("RFC_PANEL_BR") as IMyTextPanel;
            COMMAND_MODULE = GridTerminalSystem.GetBlockWithName("CENTRAL_COMMAND") as IMyProgrammableBlock;

            try
            {
                List<IMyTerminalBlock> TEMP_RC = new List<IMyTerminalBlock>();
                GridTerminalSystem.GetBlocksOfType<IMyShipController>(TEMP_RC, b => b.CubeGrid == Me.CubeGrid && b.CustomName == "RFC_RC");
                CONTROL = TEMP_RC[0] as IMyShipController;
            }
            catch { }

            GridTerminalSystem.GetBlocksOfType<IMyGyro>(GYROS, b => b.CubeGrid == Me.CubeGrid);

            // Initialize background for massive 400x400 display
            string BCKGRD_RAW = Me.CustomData;
            string BCKGRD = BCKGRD_RAW.Replace("\n", String.Empty).Replace(" ", String.Empty);

            // Initialize background array for massive screen
            for (int j = 0; j < ROWS_CT; j++)
            {
                for (int i = 0; i < COLUMNS_CT; i++)
                {
                    // Use default background or load from custom data if available
                    char ITEM = (BCKGRD.Length > (j * COLUMNS_CT) + i && BCKGRD[(j * COLUMNS_CT) + i] == '0') ? L1 : B;
                    BDRAW[j, i] = ITEM;
                }
            }
        }
        #endregion

        #region Enhanced Utility Functions
        public void DRAW_UI_SHP(Vector2D BASE, Vector2D TOP, char PRI_COL, int SIZE, char LINE_COLOUR, string SYMBOL)
        {
            // Draw symbol line faintly
            line(TOP, BASE, LINE_COLOUR);

            // Draw enhanced symbol core for massive display
            int HEIGHT = MathHelper.Clamp(SIZE / UI_SCALE, 2, 40); // Larger for massive display
            int LENGTH = MathHelper.Clamp(SIZE / UI_SCALE * 2, 4, 40);

            for (int j = 0; j < HEIGHT; j++)
            {
                for (int i = 0; i < LENGTH; i++)
                {
                    int y = (int)TOP.Y - HEIGHT / 2 + j;
                    int x = (int)TOP.X - LENGTH / 2 + i;
                    if (x < COLUMNS_CT && y < ROWS_CT && y > ROW_UI_START && y < ROW_UI_END && x > -1 && DRAW[y, x] != P)
                    { DRAW[y, x] = PRI_COL; }
                }
            }

            // Draw enhanced symbol identifier
            WRT_SYMB(SYST_SYMBLS.SYST_SYMBLS_PROCEDURAL[SYMBOL], (int)TOP.Y - 2, (int)TOP.X - 6);
        }

        public void line(Vector2D IN_COORD, Vector2D OUT_COORD, char color)
        {
            // Enhanced Bresenham's line algorithm for massive display
            int x = (int)IN_COORD.X;
            int y = (int)IN_COORD.Y;
            int w = (int)OUT_COORD.X - x;
            int h = (int)OUT_COORD.Y - y;
            int dx1 = 0, dy1 = 0, dx2 = 0, dy2 = 0;

            if (w < 0) dx1 = -1; else if (w > 0) dx1 = 1;
            if (h < 0) dy1 = -1; else if (h > 0) dy1 = 1;
            if (w < 0) dx2 = -1; else if (w > 0) dx2 = 1;

            int longest = Math.Abs(w);
            int shortest = Math.Abs(h);

            if (!(longest > shortest))
            {
                longest = Math.Abs(h);
                shortest = Math.Abs(w);
                if (h < 0) dy2 = -1; else if (h > 0) dy2 = 1;
                dx2 = 0;
            }

            int numerator = longest >> 1;
            for (int i = 0; i <= longest; i++)
            {
                if (!(x < COLUMNS_CT && y < ROW_UI_END)) { return; }
                if (y > ROW_UI_START && x > -1 && x < COLUMNS_CT && y < ROWS_CT &&
                    DRAW[y, x] != P && DRAW[y, x] != red && DRAW[y, x] != green)
                { DRAW[y, x] = color; }

                numerator += shortest;
                if (!(numerator < longest))
                {
                    numerator -= longest;
                    x += dx1;
                    y += dy1;
                }
                else
                {
                    x += dx2;
                    y += dy2;
                }
            }
        }

        public void WRT_SYMB(char[,] SYMBOL, int START_ROW, int START_COL)
        {
            // Write enhanced symbol at given coordinates (bounds checking for massive display)
            for (int j = START_ROW; j < START_ROW + SYMBOL.GetLength(0); j++)
            {
                for (int i = START_COL; i < START_COL + SYMBOL.GetLength(1); i++)
                {
                    if (j > ROWS_CT - 1 || i > COLUMNS_CT - 1 || i < 0 || j < 0) { continue; }
                    if (SYMBOL[j - START_ROW, i - START_COL] == B || DRAW[j, i] == green) { continue; }
                    DRAW[j, i] = SYMBOL[j - START_ROW, i - START_COL];
                }
            }
        }

        public void RASTER(ref Vector2D COORD)
        {
            // Convert world coordinates to screen coordinates for massive display
            COORD = new Vector2D(MathHelper.Clamp(COORD.X, 0, COLUMNS_CT - 1), MathHelper.Clamp(COORD.Y, 0, ROWS_CT - 1));
        }
        #endregion

        #region Support Classes
        class CURSOR
        {
            public char Cursor = '+';
            public char Select = 'o';
            public char Attack = 'x';
            public char Dock = 'd';
        }

        class LETTERING
        {
            public char[,] SELECT = new char[,] { { 'S', 'E', 'L', 'E', 'C', 'T' } };
            public char[,] ATTACK = new char[,] { { 'A', 'T', 'T', 'A', 'C', 'K' } };
            public char[,] DOCK = new char[,] { { 'D', 'O', 'C', 'K' } };
        }

        class SYMBOLS
        {
            public Dictionary<string, char[,]> SYST_SYMBLS_PROCEDURAL = new Dictionary<string, char[,]>()
            {
                { "I", new char[,] {
                    { 'I', 'I' },
                    { 'I', 'I' }
                } },
                { "F", new char[,] {
                    { 'F', 'F' },
                    { 'F', 'F' }
                } },
                { "C", new char[,] {
                    { 'C', 'C' },
                    { 'C', 'C' }
                } },
                { "U", new char[,] {
                    { 'U', 'U' },
                    { 'U', 'U' }
                } },
                { "H", new char[,] {
                    { 'H', 'H' },
                    { 'H', 'H' }
                } }
            };
        }
        #endregion
    }
}
