# Fleet Central Command Script Update Summary

## Overview
Successfully updated the FleetCentralCommandScript.cs to work with the current Space Engineers API. This script serves as the **central hub** for the entire fleet command system, coordinating all drone operations and fleet management.

## ✅ **Key Updates Made**

### **1. 🔄 Modern IGC Communication System**
- **Updated Main Method Signature**: `void Main(string argument, UpdateType updateSource)`
- **Improved IGC Message Processing**: Proper `HasPendingMessage` and `AcceptMessage()` handling
- **Enhanced Message Loop**: Process one message per tick for better performance
- **Updated IGC Tags**: Changed to standardized `RFC_FLEET_IN` and `RFC_FLEET_OUT`

### **2. ⚡ API Compatibility Fixes**
- **Removed Deprecated PBList**: Removed `RADIO.SetValue<long>("PBList", Me.EntityId)`
- **Updated IGC Broadcast**: Added `TransmissionDistance.TransmissionDistanceMax` parameter
- **Modern Constructor**: Proper IGC listener setup without deprecated callbacks
- **Version Update**: Changed from `Ver_007` to `Ver_008_Updated`

### **3. 🛡️ Enhanced Error Handling**
- **Robust IGC Processing**: Better handling of message queue
- **Null Safety**: Improved checks for missing components
- **Graceful Degradation**: System continues working even if some components fail

## **📋 What This Script Does**

### **Central Command Functions**
- **🎯 Fleet Coordination**: Manages all drone ships in the fleet
- **📡 Communication Hub**: Broadcasts commands and receives status updates
- **🗺️ Tactical Overview**: Tracks all fleet positions and status
- **⚔️ Combat Coordination**: Manages target assignment and engagement
- **🚢 Docking Management**: Controls carrier operations and ship docking

### **Key Features**
- **Drone Management**: Track and command individual AI ships
- **Target Detection**: Sensor-based enemy detection and tracking
- **Formation Control**: Manage fleet formations and spacing
- **Docking System**: Automated carrier and docking operations
- **Real-time Status**: Live fleet health and position monitoring

## **🔧 Required Blocks**

### **Essential Components**
1. **Remote Control** - Named `"RFC_RC"`
   - **Purpose**: Ship control and navigation reference
   - **Critical**: Yes - Required for fleet positioning

2. **Radio Antenna** - Custom Data: `"RFC_ANT"`
   - **Purpose**: Fleet identification (legacy, now uses IGC)
   - **Setup**: Add `RFC_ANT` to antenna's Custom Data field
   - **Critical**: Yes - Required for system identification

3. **Programmable Block** - For this script
   - **Purpose**: Runs the central command logic
   - **Critical**: Yes - Core of the fleet command system

### **Optional Components**
4. **Sensor Block** - Named `"RFC_SENSOR"`
   - **Purpose**: Automatic enemy detection
   - **Range**: Configure for desired detection range
   - **Critical**: No - But enhances tactical awareness

5. **Camera Blocks** - Any number
   - **Purpose**: Enhanced target scanning and detection
   - **Setup**: Script auto-enables raycast on all cameras
   - **Critical**: No - But improves targeting accuracy

6. **Turrets** - Custom Data: `"DIRECTOR"`
   - **Purpose**: Target designation and tracking
   - **Setup**: Add `DIRECTOR` to turret's Custom Data
   - **Critical**: No - But enhances combat effectiveness

## **🚀 Installation & Setup**

### **Step 1: Block Placement**
```
1. Place a Remote Control block and name it "RFC_RC"
2. Place a Radio Antenna and add "RFC_ANT" to its Custom Data
3. Place a Programmable Block for this script
4. Optional: Add RFC_SENSOR, cameras, and DIRECTOR turrets
```

### **Step 2: Script Installation**
```
1. Copy the updated FleetCentralCommandScript.cs content
2. Paste into the Programmable Block
3. Compile and run the script
4. Verify initialization messages appear
```

### **Step 3: Fleet Integration**
```
1. Ensure drone ships have updated fleet command scripts
2. Verify all ships use same IGC channels (RFC_FLEET_IN/OUT)
3. Test communication between command ship and drones
4. Configure formations and tactical settings as needed
```

## **📊 System Status Indicators**

### **Display Information**
- **Version**: Shows current script version (Ver_008_Updated)
- **Drone Count**: Number of active drone ships
- **Dock Count**: Number of available docking points
- **DEI Count**: Number of detected enemy entities
- **Runtime**: Script performance metrics

### **Communication Status**
- **IGC Active**: Modern communication system operational
- **Fleet Coordination**: Real-time command and control
- **Status Updates**: Live fleet health and position data

## **🔗 Integration with Other Scripts**

### **Works With**
- ✅ **Updated Drone AI Scripts** - Individual ship control
- ✅ **Updated Display Scripts** - Tactical interface (regular, wide, quad LCD)
- ✅ **Fleet Command Scripts** - All variants of the fleet system
- ✅ **Current Space Engineers** - Modern API compatibility

### **Communication Protocol**
- **IGC Channels**: RFC_FLEET_IN (receive) / RFC_FLEET_OUT (broadcast)
- **Message Format**: Standardized fleet command protocol
- **Data Exchange**: Position, status, commands, target information

## **⚙️ Configuration Options**

### **Formation Settings**
```csharp
string DefaultFormation = ""; // Line abreast, line astern, drone wall
Dictionary<string, double> SPACINGS = { {"I", 40}, {"C", 200}, {"F", 150}, {"U", -150} };
Dictionary<string, double> SPACINGS_X = { {"I", 100}, {"C", 0}, {"F", 0}, {"U", -150} };
```

### **IGC Configuration**
```csharp
const string IGCTagOUT = "RFC_FLEET_OUT"; // Broadcast channel
const string IGCTagIN = "RFC_FLEET_IN";   // Receive channel
```

## **🐛 Troubleshooting**

### **Common Issues**

**"No RFC_RC Found"**
- Solution: Ensure Remote Control is named exactly `RFC_RC`

**"No Communication with Drones"**
- Solution: Verify all ships use same IGC channels and updated scripts

**"Sensor Not Working"**
- Solution: Check sensor is named `RFC_SENSOR` and properly configured

**"Performance Issues"**
- Solution: Script runs every 10 ticks - check for overloaded grids

### **Performance Notes**
- **Update Frequency**: 10 ticks (optimized for performance)
- **IGC Processing**: One message per tick to prevent lag
- **Memory Usage**: Efficient data structures for large fleets
- **Scalability**: Handles dozens of ships effectively

## **📈 Benefits of Updated Version**

### **Reliability Improvements**
- **🔧 Modern API**: Compatible with current Space Engineers
- **📡 Better Communication**: Reliable IGC system
- **🛡️ Error Handling**: Graceful failure recovery
- **⚡ Performance**: Optimized for better responsiveness

### **Feature Enhancements**
- **🎯 Enhanced Coordination**: Better fleet management
- **📊 Real-time Status**: Live fleet monitoring
- **🔍 Improved Detection**: Better enemy tracking
- **🚀 Future-proof**: Ready for SE updates

## **Summary**

The updated Fleet Central Command script provides:
- **✅ Modern API Compatibility** - Works with current Space Engineers
- **✅ Reliable Communication** - IGC-based fleet coordination  
- **✅ Enhanced Performance** - Optimized for better responsiveness
- **✅ Robust Operation** - Improved error handling and stability

This is the **command center** of your fleet - install it on your flagship or command ship to coordinate your entire AI drone fleet with modern, reliable technology!
