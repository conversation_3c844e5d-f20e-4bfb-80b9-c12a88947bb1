# Fleet Command Integration Setup Guide

## 🚨 **Critical Issue: Display Not Working with Central Command**

### **The Problem**
The Display script can't control the fleet because it's not properly connected to the Central Command script.

### **The Solution**
The scripts are designed to work together through **shared data** and **specific block naming**. Here's the exact setup:

---

## **✅ Required Setup (EXACT Names Required)**

### **1. 📦 Programmable Blocks (2 Required)**

#### **Block 1: Central Command**
- **Block Type**: Programmable Block
- **Exact Name**: `CENTRAL_COMMAND`
- **Script**: FleetCentralCommandScript.cs
- **Purpose**: Fleet management and data processing

#### **Block 2: Display Interface**
- **Block Type**: Programmable Block  
- **Name**: Any name (e.g., "RFC_DISPLAY")
- **Script**: FleetDisplayScript.cs
- **Purpose**: Visual interface and controls

### **2. 🖥️ Display Panel**
- **Block Type**: LCD Panel (Text Panel)
- **Exact Name**: `RFC_PANEL`
- **Purpose**: Shows the tactical display

### **3. 🎮 Control Block**
- **Block Type**: Remote Control or Cockpit
- **Exact Name**: `RFC_RC`
- **Purpose**: Mouse control for the display interface

### **4. 📡 Communication**
- **Block Type**: Radio Antenna
- **Custom Data**: Must contain `RFC_ANT`
- **Purpose**: Fleet identification

---

## **🔧 Step-by-Step Setup Instructions**

### **Step 1: Place and Name Blocks**
```
1. Place 2 Programmable Blocks
2. Rename the first one to exactly: "CENTRAL_COMMAND"
3. Rename the second one to: "RFC_DISPLAY" (or any name)
4. Place an LCD Panel and rename it to: "RFC_PANEL"
5. Place a Remote Control and rename it to: "RFC_RC"
6. Place a Radio Antenna and add "RFC_ANT" to its Custom Data
```

### **Step 2: Install Scripts**
```
1. Copy FleetCentralCommandScript.cs into "CENTRAL_COMMAND" block
2. Copy FleetDisplayScript.cs into "RFC_DISPLAY" block
3. Compile both scripts (check for errors)
```

### **Step 3: Start the System**
```
1. Run the "CENTRAL_COMMAND" script first
2. Wait for it to initialize (check the output)
3. Run the "RFC_DISPLAY" script second
4. Both should show "System Active" messages
```

### **Step 4: Test the Interface**
```
1. Enter the RFC_RC Remote Control block
2. Use mouse movement to control the cursor on the display
3. Use roll left/right for clicking
4. You should see the tactical map and be able to select ships
```

---

## **🐛 Troubleshooting Common Issues**

### **"No CENTRAL_COMMAND Found"**
- **Problem**: Display script can't find the central command block
- **Solution**: Ensure the programmable block is named exactly `CENTRAL_COMMAND`

### **"No RFC_PANEL Found"**
- **Problem**: Display script can't find the LCD panel
- **Solution**: Ensure the LCD panel is named exactly `RFC_PANEL`

### **"No RFC_RC Found"**
- **Problem**: Scripts can't find the control block
- **Solution**: Ensure the Remote Control is named exactly `RFC_RC`

### **"Display Shows But No Control"**
- **Problem**: Interface not responding to controls
- **Solution**: 
  1. Make sure you're seated in the `RFC_RC` block
  2. Check that both scripts are running
  3. Verify the Central Command script is processing data

### **"No Fleet Data"**
- **Problem**: Display shows but no ships appear
- **Solution**:
  1. Ensure drone ships have updated scripts
  2. Check IGC communication channels match
  3. Verify Central Command is receiving drone data

---

## **📊 How the Integration Works**

### **Data Flow**
```
1. Central Command Script:
   - Receives drone data via IGC
   - Processes fleet information
   - Stores data in its CustomData

2. Display Script:
   - Reads data from CENTRAL_COMMAND block's CustomData
   - Renders tactical display on RFC_PANEL
   - Sends commands back to CENTRAL_COMMAND
   - Handles user input from RFC_RC
```

### **Control Flow**
```
1. Player enters RFC_RC Remote Control
2. Mouse movement controls cursor on display
3. Roll left/right = left/right click
4. Display script processes clicks
5. Commands sent to Central Command
6. Central Command broadcasts to fleet
```

---

## **⚙️ Advanced Configuration**

### **Custom Data Communication**
The scripts communicate through the CENTRAL_COMMAND block's CustomData field:
```
Section 1: Drone data (before ##INFO##)
Section 2: Docking data (between ##INFO## markers)  
Section 3: Enemy data (after second ##INFO##)
```

### **IGC Channels**
Both scripts use these channels:
- **RFC_FLEET_IN**: Commands to fleet
- **RFC_FLEET_OUT**: Data from fleet

### **Performance Notes**
- Central Command: Updates every 10 ticks
- Display: Updates every 10 ticks
- Both scripts coordinate to avoid conflicts

---

## **✅ Verification Checklist**

### **Before Starting**
- [ ] CENTRAL_COMMAND programmable block exists and is named correctly
- [ ] RFC_DISPLAY programmable block exists with display script
- [ ] RFC_PANEL LCD panel exists and is named correctly
- [ ] RFC_RC Remote Control exists and is named correctly
- [ ] RFC_ANT antenna exists with correct Custom Data

### **After Starting**
- [ ] Central Command script shows "Ver_008_Updated Initialized"
- [ ] Display script shows initialization messages
- [ ] LCD panel shows tactical display
- [ ] Entering RFC_RC allows cursor control
- [ ] Fleet data appears on display (if drones are active)

---

## **🚀 Expected Results**

When properly set up, you should see:

1. **Tactical Display**: 100x100 grid showing fleet positions
2. **Ship Icons**: Colored squares representing your ships
3. **Health Bars**: Visual health indicators for each ship
4. **Interactive Controls**: Mouse cursor controlled by RFC_RC
5. **Command Interface**: Ability to select ships and issue orders
6. **Real-time Updates**: Live fleet status and position updates

The display becomes fully interactive when you're seated in the RFC_RC block, allowing you to command your entire AI fleet through an intuitive graphical interface!

---

## **📞 Still Having Issues?**

If the setup still doesn't work:

1. **Check Script Output**: Look at both programmable blocks' output for error messages
2. **Verify Block Names**: Double-check all block names are exactly correct
3. **Test Components**: Ensure each required block is functional
4. **IGC Communication**: Verify drone ships are using updated scripts with matching IGC channels
5. **Power and Ownership**: Ensure all blocks are powered and owned by the same player/faction
