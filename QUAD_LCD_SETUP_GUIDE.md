# Fleet Command Quad LCD Setup Guide

## Overview
This is a **massive tactical display version** of the Fleet Command Display Script that uses **4 LCD Panels arranged in a 2x2 grid** to create a **400x400 pixel tactical interface**. This provides an enormous battlefield overview perfect for large-scale fleet operations and complex tactical scenarios.

## Key Features

### **Massive Display Resolution**
- **Total Resolution**: 400x400 pixels (16x larger than original)
- **Panel Configuration**: 4 LCD panels in 2x2 grid
- **Individual Panel Size**: 200x200 pixels each
- **Total Display Area**: 160,000 pixels vs 10,000 in original

### **Enhanced Tactical Capabilities**
- **🔍 Massive Battlefield Overview** - See entire large-scale engagements
- **📊 Enhanced Fleet Visualization** - Track hundreds of ships simultaneously
- **🎯 Precision Targeting** - Detailed tactical positioning and movement
- **📈 Strategic Command View** - Perfect for fleet admirals and tactical coordinators

## Required Blocks

### **Essential LCD Panels (Must Have Exact Names)**
1. **Top-Left Panel** - Named `"RFC_PANEL_TL"`
2. **Top-Right Panel** - Named `"RFC_PANEL_TR"`
3. **Bottom-Left Panel** - Named `"RFC_PANEL_BL"`
4. **Bottom-Right Panel** - Named `"RFC_PANEL_BR"`

### **Control Blocks (Same as Original)**
5. **Remote Control** - Named `"RFC_RC"`
6. **Programmable Block** - Named `"CENTRAL_COMMAND"`
7. **Gyroscopes** - For mouse control simulation
8. **Power Source** - Adequate power for 4 LCD panels

## Physical Setup Instructions

### **Step 1: LCD Panel Placement**
```
Arrange 4 LCD Panels in a 2x2 square formation:

[RFC_PANEL_TL] [RFC_PANEL_TR]
[RFC_PANEL_BL] [RFC_PANEL_BR]

- Place panels touching each other for seamless display
- Ensure all panels face the same direction
- Orient panels so the display forms one continuous screen
```

### **Step 2: Panel Naming**
```
Rename each LCD Panel with EXACT names:
✅ Top-Left: "RFC_PANEL_TL"
✅ Top-Right: "RFC_PANEL_TR"  
✅ Bottom-Left: "RFC_PANEL_BL"
✅ Bottom-Right: "RFC_PANEL_BR"

⚠️ Names must be EXACT - script will fail with incorrect names
```

### **Step 3: Script Installation**
```
1. Copy FleetDisplayScript_Quad.cs content
2. Paste into a Programmable Block
3. Compile and run the script
4. All 4 LCD panels should initialize and show tactical grid
5. Verify seamless display across all 4 panels
```

## Display Layout

### **Panel Responsibilities**
```
┌─────────────────┬─────────────────┐
│   TOP-LEFT      │   TOP-RIGHT     │
│  (0-199,        │  (0-199,        │
│   0-199)        │   200-399)      │
├─────────────────┼─────────────────┤
│  BOTTOM-LEFT    │  BOTTOM-RIGHT   │
│  (200-399,      │  (200-399,      │
│   0-199)        │   200-399)      │
└─────────────────┴─────────────────┘
```

### **Coordinate System**
- **Total Area**: 400x400 pixels
- **Center Point**: (200, 200)
- **Each Panel**: 200x200 pixels
- **Seamless Integration**: No gaps between quadrants

## Enhanced Features

### **Massive Scale Tactical Display**
- **16x Display Area** - Enormous tactical overview
- **Enhanced Grid System** - 8x8 reference grid for precision
- **Larger Ship Icons** - Better visibility on massive display
- **Extended Health Bars** - 15-20 pixel health indicators
- **Enhanced Zoom Range** - Scale from 50 to 2000 units

### **Improved UI Elements**
- **Larger Click Indicators** - 4-pixel cross-hairs for visibility
- **Enhanced Selection Boxes** - Better visibility on large display
- **Bigger Zoom Controls** - 8-pixel detection areas
- **Improved Mouse Sensitivity** - 0.2 multiplier for precise control

### **Advanced Fleet Management**
- **Massive Fleet Support** - Handle hundreds of ships simultaneously
- **Large-Scale Formations** - Visualize complex fleet arrangements
- **Strategic Overview** - Perfect for fleet command operations
- **Enhanced Targeting** - 10-pixel detection areas for precision

## Controls (Enhanced for Massive Display)

### **Mouse Control**
- **Ship Pitch/Yaw**: Mouse movement (enhanced sensitivity)
- **Ship Roll Left**: Left mouse click
- **Ship Roll Right**: Right mouse click
- **Mouse Speed**: 0.2x multiplier for precision on large display

### **Enhanced Interface Actions**
- **Left Click**: Select squadrons (larger detection areas)
- **Right Click**: Issue commands (move/attack with precision)
- **Zoom Controls**: Located in top-right corner (larger buttons)
- **Auto-Scroll**: Enhanced edge scrolling for massive display

## Technical Specifications

### **Display Configuration**
```csharp
const int ROWS_CT = 400;           // Total height: 400 pixels
const int COLUMNS_CT = 400;        // Total width: 400 pixels  
const int PANEL_SIZE = 200;        // Each panel: 200x200
char[] ALLOC_TL/TR/BL/BR = new char[40401]; // 4 separate buffers
```

### **Enhanced Settings**
```csharp
UI_SCALE = 400;                    // Larger default scale
FontSize: 0.173f                   // Standard LCD font size
Font: 1147350002                   // Monospace font
CLICK_TIMER = 120;                 // Longer indicators
```

### **Performance Optimization**
```csharp
Update Frequency: Update10         // 10-tick updates
Quadrant Rendering: Parallel       // Each panel rendered separately
Buffer Management: 4 separate      // Independent panel buffers
```

## Advantages of Quad LCD Version

### **Strategic Benefits**
- **🌍 Massive Battlefield View** - See entire large-scale engagements
- **🎯 Precision Command** - Detailed tactical control and positioning
- **📊 Fleet Scale Management** - Handle massive fleets effectively
- **🔍 Enhanced Situational Awareness** - Complete tactical picture

### **Technical Advantages**
- **16x Display Area** - Enormous tactical real estate
- **Seamless Integration** - 4 panels work as one display
- **Enhanced Performance** - Optimized rendering system
- **Scalable Architecture** - Easy to expand or modify

## Troubleshooting

### **Common Issues**

**"Missing LCD Panels" Error**
- Solution: Ensure all 4 panels are named exactly: RFC_PANEL_TL, RFC_PANEL_TR, RFC_PANEL_BL, RFC_PANEL_BR

**"Display appears fragmented"**
- Solution: Check panel placement - they should be touching and aligned properly

**"Only some panels working"**
- Solution: Verify all 4 panels are powered and functional

**"Display seems slow"**
- Solution: Ensure adequate power supply for all 4 LCD panels

### **Performance Notes**
- Quad LCD version uses 4x memory for display buffers
- Requires more power due to 4 active LCD panels
- Rendering is optimized with separate quadrant processing
- Performance scales well with modern Space Engineers

## Setup Verification Checklist

### **Physical Setup**
- ✅ 4 LCD Panels placed in 2x2 formation
- ✅ Panels are touching and aligned
- ✅ All panels face same direction
- ✅ Adequate power supply connected

### **Block Naming**
- ✅ RFC_PANEL_TL (Top-Left)
- ✅ RFC_PANEL_TR (Top-Right)
- ✅ RFC_PANEL_BL (Bottom-Left)
- ✅ RFC_PANEL_BR (Bottom-Right)
- ✅ RFC_RC (Remote Control)
- ✅ CENTRAL_COMMAND (Programmable Block)

### **Functionality Test**
- ✅ All 4 panels show tactical grid
- ✅ Grid lines align across panels seamlessly
- ✅ Mouse cursor moves smoothly across all quadrants
- ✅ Zoom controls work in top-right corner
- ✅ Fleet data displays across entire 400x400 area

## Migration from Other Versions

### **From Original (100x100)**
- Replace single LCD with 4-panel setup
- Update script to Quad version
- Enjoy 16x larger tactical display

### **From Wide LCD (178x100)**
- Replace wide LCD with 4-panel setup
- Update script to Quad version
- Gain vertical space and massive total area

### **Data Preservation**
- All fleet data and settings preserved
- No changes needed to drone ships
- Communication system remains identical

## Summary

The Quad LCD version provides the **ultimate tactical display experience** with:
- **🔥 16x Display Area** - Massive 400x400 pixel battlefield view
- **🎯 Strategic Command Capability** - Perfect for fleet admirals
- **📊 Large-Scale Fleet Management** - Handle hundreds of ships
- **🌍 Complete Battlefield Awareness** - See everything at once

**Perfect for:**
- Large fleet operations (50+ ships)
- Complex tactical scenarios
- Strategic command centers
- Multi-faction engagements
- Massive space battles

The ultimate fleet command experience for serious Space Engineers tacticians!
