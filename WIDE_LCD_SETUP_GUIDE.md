# Fleet Command Wide LCD Setup Guide

## Overview
This is a modified version of the Fleet Command Display Script optimized for **Wide LCD Panels** in Space Engineers. The wide version provides an extended tactical view with better utilization of the wider screen real estate.

## Key Differences from Original

### **Display Resolution**
- **Original**: 100x100 pixels (square LCD)
- **Wide Version**: 178x100 pixels (wide LCD)
- **Aspect Ratio**: Changed from 1:1 to 1.78:1

### **UI Layout Changes**
- **Extended tactical map area** - More horizontal space for fleet positioning
- **Repositioned zoom controls** - Moved to the right side (columns 156-170)
- **Additional grid lines** - More vertical reference lines for better navigation
- **Adjusted font size** - Smaller font (0.097f) to fit the wider content properly

### **Enhanced Features**
- **Better fleet spread visualization** - Wide screen shows more ships simultaneously
- **Improved tactical overview** - Extended horizontal view for large-scale operations
- **More detailed grid system** - Additional reference lines for precise positioning

## Required Blocks

### **Essential Blocks (Different from Original)**
1. **Wide LCD Panel** - Named `"RFC_PANEL_WIDE"`
   - **Type**: Wide LCD Panel (not regular LCD)
   - **Name**: Must be exactly `RFC_PANEL_WIDE`
   - **Purpose**: Main tactical display interface

### **Same as Original**
2. **Remote Control** - Named `"RFC_RC"`
3. **Programmable Block** - Named `"CENTRAL_COMMAND"`
4. **Gyroscopes** - For mouse control simulation
5. **Power Source** - Reactor/Battery for operation

## Installation Steps

### **1. Place Required Blocks**
```
1. Place a Wide LCD Panel
2. Rename it to "RFC_PANEL_WIDE" (exact name required)
3. Ensure you have RFC_RC remote control block
4. Ensure you have CENTRAL_COMMAND programmable block
5. Add gyroscopes to your ship for mouse control
```

### **2. Install Script**
```
1. Copy the FleetDisplayScript_Wide.cs content
2. Paste into a Programmable Block
3. Compile and run the script
4. The wide LCD should initialize and show the tactical interface
```

### **3. Verify Setup**
```
✅ Wide LCD Panel shows tactical grid
✅ Mouse cursor responds to ship rotation controls
✅ Zoom controls appear on the right side
✅ Fleet data displays properly across the wide screen
```

## Controls (Same as Original)

### **Mouse Control**
- **Ship Pitch/Yaw**: Mouse movement
- **Ship Roll Left**: Left mouse click
- **Ship Roll Right**: Right mouse click

### **Interface Actions**
- **Left Click**: Select squadrons (drag to select multiple)
- **Right Click**: Issue commands (move/attack)
- **Zoom Controls**: Located on the right side of the screen

## Technical Specifications

### **Display Configuration**
```csharp
const int ROWS_CT = 100;      // Height: 100 pixels
const int COLUMNS_CT = 178;   // Width: 178 pixels (wide)
char[] ALLOC = new char[17901]; // Total screen buffer
```

### **Font Settings**
```csharp
FontSize: 0.097f              // Optimized for wide LCD
Font: 1147350002              // Monospace font
```

### **UI Positioning**
```csharp
COL_ZOOM_ICON1 = 156;         // Zoom in button (right side)
COL_ZOOM_ICON2 = 170;         // Zoom out button (right side)
OFFSETX = 89;                 // Default X center (wide screen)
OFFSETY = 50;                 // Default Y center
```

## Advantages of Wide LCD Version

### **Tactical Benefits**
- **🔍 Extended View**: See more of the battlefield horizontally
- **📊 Better Fleet Management**: More space for squadron positioning
- **🎯 Improved Targeting**: Wider tactical overview for combat coordination
- **📈 Enhanced Situational Awareness**: Better understanding of fleet spread

### **UI Improvements**
- **More Grid Lines**: Additional vertical reference lines
- **Better Proportions**: More natural widescreen layout
- **Optimized Controls**: Zoom controls positioned for wide screen use
- **Extended Map Area**: Larger tactical display region

## Compatibility

### **Works With**
- ✅ All existing Fleet Command drone scripts
- ✅ Current Space Engineers API
- ✅ IGC communication system
- ✅ Existing fleet management features

### **Requirements**
- ✅ Wide LCD Panel (not regular LCD)
- ✅ Space Engineers current version
- ✅ Same ship setup as original system

## Troubleshooting

### **Common Issues**

**"No RFC_PANEL_WIDE Found"**
- Solution: Ensure you have a Wide LCD Panel named exactly `RFC_PANEL_WIDE`

**"Display appears stretched"**
- Solution: Verify you're using a Wide LCD Panel, not a regular LCD Panel

**"Zoom controls not visible"**
- Solution: Check that the LCD is properly configured and the script is running

**"Font too large/small"**
- Solution: The script auto-adjusts font size for wide LCD (0.097f)

### **Performance Notes**
- Wide LCD version uses slightly more memory due to larger display buffer
- Performance impact is minimal on modern Space Engineers
- Rendering is optimized for the wider resolution

## Migration from Original

### **To Switch from Regular to Wide LCD**
1. Replace your regular LCD Panel with a Wide LCD Panel
2. Rename the new panel to `RFC_PANEL_WIDE`
3. Replace the script with the wide LCD version
4. Recompile and run

### **Data Preservation**
- All fleet data and settings are preserved
- No changes needed to drone ships
- Communication system remains the same

## Summary

The Wide LCD version provides a superior tactical display experience with:
- **78% more horizontal display area**
- **Better fleet visualization**
- **Enhanced tactical overview**
- **Optimized UI layout for wide screens**

Perfect for large fleet operations and complex tactical scenarios!
