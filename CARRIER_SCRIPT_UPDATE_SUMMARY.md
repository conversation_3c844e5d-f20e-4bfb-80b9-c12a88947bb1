# Carrier Script Update Summary

## Overview
Successfully updated the CarrierScript to work with the current Space Engineers API. This script provides **AI-controlled carrier and support ship functionality** for the Fleet Command system, enabling automated docking operations, fleet support, and tactical coordination.

## ✅ **Key Updates Made**

### **1. 🔄 Modern IGC Communication System**
- **Updated Main Method Signature**: `void Main(string argument, UpdateType updateSource)`
- **Improved IGC Message Processing**: Proper `HasPendingMessage` and `AcceptMessage()` handling
- **Enhanced Communication Flow**: Better message queue management
- **Updated IGC Tags**: Changed to standardized `RFC_FLEET_IN` and `RFC_FLEET_OUT`

### **2. ⚡ Performance Improvements**
- **Update Frequency**: Changed from `Update100` to `Update10` for better responsiveness
- **Enhanced IGC Handling**: More efficient message processing
- **Improved Error Handling**: Better debugging information and error recovery
- **Version Update**: Changed from `Ver_007` to `Ver_008_Updated`

### **3. 🛡️ API Compatibility**
- **Modern IGC Broadcast**: Added `TransmissionDistance.TransmissionDistanceMax` parameter
- **Robust Message Handling**: Improved null checks and error handling
- **Current SE Compatibility**: Works with latest Space Engineers version
- **Future-proof Design**: Ready for upcoming SE updates

## **🚢 What This Script Does**

### **Carrier Ship Functions**
- **🛬 Automated Docking**: Manages docking operations for drone ships
- **📡 Fleet Communication**: Relays commands between central command and drones
- **🎯 Target Coordination**: Shares target information across the fleet
- **🔧 Support Operations**: Provides logistical support for combat operations
- **📊 Status Reporting**: Reports carrier status and docked ship information

### **AI Behaviors**
- **Intelligent Positioning**: Maintains optimal position for fleet support
- **Automated Defense**: Engages threats to protect docked ships
- **Formation Flying**: Maintains position within fleet formations
- **Resource Management**: Manages power and systems efficiently
- **Emergency Protocols**: Handles emergency undocking and defensive actions

## **🔧 Required Blocks**

### **Essential Components**
1. **Remote Control** - Named `"RFC_RC"`
   - **Purpose**: Ship control and navigation
   - **Critical**: Yes - Required for all movement and positioning

2. **Ship Connector** - Named `"RFC_CONNECTOR"`
   - **Purpose**: Docking operations with other ships
   - **Critical**: Yes - Core carrier functionality

3. **Turret** - Named `"RFC_TURRET"`
   - **Purpose**: Target designation and defense
   - **Critical**: Yes - Required for combat operations

4. **Radio Antenna** - Custom Data: `"RFC_ANT"`
   - **Purpose**: Fleet identification and legacy compatibility
   - **Setup**: Add `RFC_ANT` to antenna's Custom Data field
   - **Critical**: Yes - Required for system identification

5. **Gyroscope** - Named `"RFC_GYRO"`
   - **Purpose**: Ship rotation and orientation control
   - **Critical**: Yes - Required for precise maneuvering

6. **Programmable Block** - For this script
   - **Purpose**: Runs the carrier AI logic
   - **Critical**: Yes - Core of the carrier system

### **Optional Components**
7. **Camera Blocks** - Any number
   - **Purpose**: Enhanced scanning and target detection
   - **Setup**: Script auto-enables raycast on all cameras
   - **Critical**: No - But improves tactical awareness

8. **Weapons** - Directional Fire
   - **Types**: Missile Launchers, Gatling Guns, Reloadable Launchers
   - **Purpose**: Ship defense and combat support
   - **Auto-Detection**: Script finds all compatible weapons
   - **Critical**: No - But essential for combat effectiveness

9. **Timer Block** - Named `"HW_TIMER"`
   - **Purpose**: Advanced timing operations (for missile/grav ships)
   - **Required For**: FMISSILE and FGRAV ship classes
   - **Critical**: Conditional - Only for specific ship types

## **🎮 Ship Classes & Configurations**

### **Supported Ship Classes**
- **FRIGATE** - Standard carrier/support ship
- **FMISSILE** - Missile-specialized carrier
- **FGRAV** - Gravity-based carrier
- **Custom Classes** - Configurable via SHIP_CLASS variable

### **Configuration Options**
```csharp
string SHIP_CLASS = "FRIGATE";           // AI behavior type
double MAX_SPEED = 80;                   // Maximum cruising speed
double SHIP_ANGULAR_ACCEL = 0.6;         // Angular acceleration
double SHIP_DOCK_ANGULAR_SPEED = 0.3;    // Docking rotation speed
int PROJECTILE_VELOCITY = 300;           // Weapon projectile speed
```

## **🚀 Installation & Setup**

### **Step 1: Block Placement**
```
1. Place a Remote Control block and name it "RFC_RC"
2. Place a Ship Connector and name it "RFC_CONNECTOR"
3. Place a Turret and name it "RFC_TURRET"
4. Place a Radio Antenna and add "RFC_ANT" to its Custom Data
5. Place a Gyroscope and name it "RFC_GYRO"
6. Place a Programmable Block for this script
```

### **Step 2: Script Configuration**
```
1. Set SHIP_CLASS variable to desired type (FRIGATE, FMISSILE, etc.)
2. Adjust performance parameters if needed
3. Configure weapon systems and defensive capabilities
4. Set up docking approach vectors and safety zones
```

### **Step 3: Script Installation**
```
1. Copy the updated CarrierScript content
2. Paste into the Programmable Block
3. Compile and run the script
4. Verify initialization messages appear
5. Test docking operations with drone ships
```

## **📊 System Status Indicators**

### **Display Information**
- **Version**: Shows current script version (Ver_008_Updated)
- **Ship Class**: Current AI behavior configuration
- **Docking Status**: Active docking operations
- **Fleet Position**: Current position within fleet formation
- **Combat Status**: Engagement and threat information

### **Communication Status**
- **IGC Active**: Modern communication system operational
- **Fleet Coordination**: Real-time command relay
- **Docking Coordination**: Active docking management
- **Target Sharing**: Combat information distribution

## **🔗 Integration with Fleet System**

### **Works With**
- ✅ **Central Command Script** - Receives fleet-wide commands
- ✅ **Drone AI Scripts** - Coordinates with individual ships
- ✅ **Display Scripts** - Provides status to tactical displays
- ✅ **Fleet Command System** - Full integration with fleet operations

### **Communication Protocol**
- **IGC Channels**: RFC_FLEET_IN (receive) / RFC_FLEET_OUT (broadcast)
- **Message Format**: Compatible with fleet command protocol
- **Data Types**: Position, docking status, target information, fleet commands

## **⚙️ Advanced Features**

### **Intelligent Docking System**
- **Automated Approach**: Guides incoming ships to docking
- **Safety Protocols**: Prevents collisions during docking
- **Queue Management**: Handles multiple ships requesting docking
- **Emergency Undocking**: Rapid deployment in combat situations

### **Fleet Support Operations**
- **Formation Maintenance**: Maintains position within fleet
- **Command Relay**: Forwards commands to assigned ships
- **Target Coordination**: Shares combat information
- **Logistics Support**: Manages fleet resources and positioning

## **🐛 Troubleshooting**

### **Common Issues**

**"No RFC_RC Found"**
- Solution: Ensure Remote Control is named exactly `RFC_RC`

**"No RFC_CONNECTOR Found"**
- Solution: Check Ship Connector is named `RFC_CONNECTOR` and functional

**"No RFC_TURRET Found"**
- Solution: Verify Turret is named `RFC_TURRET` and operational

**"Docking Issues"**
- Solution: Check connector alignment and approach vectors

**"Communication Problems"**
- Solution: Verify IGC channels match fleet system configuration

### **Performance Notes**
- **Update Frequency**: 10 ticks for responsive operation
- **IGC Processing**: Efficient message handling
- **Docking Operations**: Optimized for multiple simultaneous operations
- **Combat Response**: Fast reaction to threats and commands

## **📈 Benefits of Updated Version**

### **Reliability Improvements**
- **🔧 Modern API**: Compatible with current Space Engineers
- **📡 Better Communication**: Reliable IGC system
- **🛡️ Error Handling**: Improved stability and debugging
- **⚡ Performance**: Better responsiveness and efficiency

### **Feature Enhancements**
- **🚢 Enhanced Docking**: More reliable docking operations
- **🎯 Better Coordination**: Improved fleet integration
- **📊 Real-time Status**: Live operational monitoring
- **🚀 Future-proof**: Ready for SE updates

## **Summary**

The updated Carrier Script provides:
- **✅ Modern API Compatibility** - Works with current Space Engineers
- **✅ Reliable Fleet Integration** - IGC-based communication
- **✅ Enhanced Docking Operations** - Automated carrier functionality
- **✅ Robust Performance** - Optimized for stability and responsiveness

Perfect for **fleet carriers**, **support ships**, and **mobile command platforms** that need to coordinate with AI drone fleets while providing docking and logistical support!
